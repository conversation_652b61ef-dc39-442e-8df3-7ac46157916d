package com.tongdao.datainterface.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.io.StringReader;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.util.Base64;

/**
 * RSA加密解密工具类
 * 兼容Python Crypto库的PKCS1_OAEP加密方式
 */
public class CryptoUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(CryptoUtil.class);
    
    // RSA加密算法
    private static final String RSA_ALGORITHM = "RSA";
    // OAEP填充方式，兼容Python Crypto.Cipher.PKCS1_OAEP
    private static final String RSA_TRANSFORMATION = "RSA/ECB/OAEPWithSHA-1AndMGF1Padding";
    
    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }
    
    /**
     * 从PEM格式字符串导入RSA私钥
     * 兼容Python RSA.import_key()方法
     * 
     * @param privateKeyPem PEM格式的私钥字符串
     * @return RSA私钥对象
     * @throws Exception 密钥解析异常
     */
    public static PrivateKey importPrivateKey(String privateKeyPem) throws Exception {
        try (PEMParser pemParser = new PEMParser(new StringReader(privateKeyPem))) {
            Object object = pemParser.readObject();
            JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
            
            if (object instanceof PEMKeyPair) {
                KeyPair keyPair = converter.getKeyPair((PEMKeyPair) object);
                return keyPair.getPrivate();
            } else if (object instanceof PrivateKey) {
                return (PrivateKey) object;
            } else {
                throw new IllegalArgumentException("Invalid private key format");
            }
        }
    }
    
    /**
     * RSA解密函数
     * 兼容Python demo中的decrypt函数
     * 
     * @param privateKeyPem PEM格式的私钥字符串
     * @param encryptedData 加密的数据（十六进制字符串）
     * @return 解密后的明文字符串
     * @throws Exception 解密异常
     */
    public static String decrypt(String privateKeyPem, String encryptedData) throws Exception {
        // 导入私钥
        PrivateKey privateKey = importPrivateKey(privateKeyPem);
        
        // 将十六进制字符串转换为字节数组
        byte[] encryptedBytes = hexStringToByteArray(encryptedData);
        
        // 创建Cipher对象并初始化为解密模式
        Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        
        // 执行解密
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        
        // 返回解密后的字符串
        return new String(decryptedBytes, "UTF-8");
    }
    
    /**
     * RSA加密函数
     * 
     * @param publicKey RSA公钥
     * @param plainText 明文字符串
     * @return 加密后的十六进制字符串
     * @throws Exception 加密异常
     */
    public static String encrypt(PublicKey publicKey, String plainText) throws Exception {
        // 创建Cipher对象并初始化为加密模式
        Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        
        // 执行加密
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));
        
        // 返回十六进制字符串
        return byteArrayToHexString(encryptedBytes);
    }
    
    /**
     * 十六进制字符串转字节数组
     * 
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }
    
    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String byteArrayToHexString(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * Base64编码
     * 
     * @param data 原始数据
     * @return Base64编码字符串
     */
    public static String base64Encode(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }
    
    /**
     * Base64解码
     * 
     * @param base64String Base64编码字符串
     * @return 原始数据
     */
    public static byte[] base64Decode(String base64String) {
        return Base64.getDecoder().decode(base64String);
    }
    
    /**
     * 获取demo中使用的私钥
     * 用于测试和兼容性验证
     * 
     * @return demo中的RSA私钥字符串
     */
    public static String getDemoPrivateKey() {
        return """
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
}
