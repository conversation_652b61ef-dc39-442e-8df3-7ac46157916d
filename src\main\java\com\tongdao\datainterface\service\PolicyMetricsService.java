package com.tongdao.datainterface.service;

import com.tongdao.datainterface.exception.BusinessException;
import com.tongdao.datainterface.model.dto.PolicyMetricsRequestDto;
import com.tongdao.datainterface.model.entity.PolicyExecutionData;
import com.tongdao.datainterface.model.entity.ResourceInfo;
import com.tongdao.datainterface.model.enums.ErrorCode;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Gauge;
import io.prometheus.client.exporter.common.TextFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.StringWriter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 策略监控指标服务类
 * 负责生成符合Prometheus格式的策略执行监控数据
 * 兼容demo.py中的数据格式和API接口
 */
@Service
public class PolicyMetricsService {

    private static final Logger logger = LoggerFactory.getLogger(PolicyMetricsService.class);

    @Autowired
    private ConfigDataService configDataService;



    /**
     * 生成策略执行监控指标的Prometheus格式数据
     * 兼容demo.py中的get_cluster_metrics函数输出格式
     *
     * @param request 请求参数（可选的过滤条件）
     * @return Prometheus格式的监控数据字符串
     */
    public String generatePolicyMetrics(PolicyMetricsRequestDto request) {
        try {
            logger.info("Generating policy metrics with request: {}", request);

            // 获取策略执行数据
            List<PolicyExecutionData> policyDataList = configDataService.getPolicyExecutionData();

            // 获取资源台账信息
            List<ResourceInfo> resourceInfoList = configDataService.getResourceInfo();

            // 应用过滤条件
            if (request != null) {
                policyDataList = applyFilters(policyDataList, request);
            }

            // 创建Prometheus注册表
            CollectorRegistry registry = new CollectorRegistry();

            // 定义指标
            defineMetrics(registry, policyDataList, resourceInfoList);

            // 生成Prometheus格式输出
            StringWriter writer = new StringWriter();
            TextFormat.write004(writer, registry.metricFamilySamples());

            String result = writer.toString();
            logger.debug("Generated metrics data length: {}", result.length());

            return result;

        } catch (Exception e) {
            logger.error("Failed to generate policy metrics: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "生成策略监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 定义Prometheus指标
     * 基于policy_data.json的实际数据结构设计，使用中文字段名
     */
    private void defineMetrics(CollectorRegistry registry, List<PolicyExecutionData> policyDataList, 
                              List<ResourceInfo> resourceInfoList) {

        // 1. 备份作业基本信息指标（使用中文字段名）
        Gauge backupJobInfo = Gauge.build()
                .name("backup_job_info")
                .help("Backup job basic information with Chinese field names")
                .labelNames("BeiFenRuanJianMingChen", "BeiFenZuoYeZhuangTai", "ZuoYeMingChen")
                .register(registry);

        // 2. 备份作业执行结果指标
        Gauge backupJobExecutionResult = Gauge.build()
                .name("backup_job_execution_result")
                .help("Backup job execution result (0=success, 1=failed, 2=partial)")
                .labelNames("ZuoYeMingChen", "BeiFenRuanJianMingChen", "ZuoYeShangCiZhiXingJieGuo")
                .register(registry);

        // 3. 备份作业执行状态指标
        Gauge backupJobExecutionStatus = Gauge.build()
                .name("backup_job_execution_status")
                .help("Backup job execution status (0=ready, 1=running, 2=completed, 3=failed, 4=waiting)")
                .labelNames("ZuoYeMingChen", "BeiFenRuanJianMingChen", "ZuoYeZhiXingZhuangTai")
                .register(registry);

        // 4. 备份作业状态指标
        Gauge backupJobStatus = Gauge.build()
                .name("backup_job_status")
                .help("Backup job status (1=enabled, 0=disabled)")
                .labelNames("ZuoYeMingChen", "BeiFenRuanJianMingChen", "BeiFenZuoYeZhuangTai")
                .register(registry);

        // 5. 备份作业时间信息指标
        Gauge backupJobTimeInfo = Gauge.build()
                .name("backup_job_time_info")
                .help("Backup job time information")
                .labelNames("ZuoYeMingChen", "ZuoYeShangCiZhiXingShiJian", "ZuoYeShangCiWanChengShiJian", "ZuoYeXiaCiZhiXingShiJian")
                .register(registry);

        // 设置指标值
        for (PolicyExecutionData policy : policyDataList) {
            String beiFenRuanJianMingChen = policy.getBackupSoftwareForMetrics();
            String beiFenZuoYeZhuangTai = policy.getBeiFenZuoYeZhuangTai() != null ? policy.getBeiFenZuoYeZhuangTai() : "unknown";
            String zuoYeMingChen = policy.getZuoYeMingChen() != null ? policy.getZuoYeMingChen() : "unknown";
            String zuoYeShangCiZhiXingJieGuo = policy.getZuoYeShangCiZhiXingJieGuo() != null ? policy.getZuoYeShangCiZhiXingJieGuo() : "unknown";
            String zuoYeZhiXingZhuangTai = policy.getZuoYeZhiXingZhuangTai() != null ? policy.getZuoYeZhiXingZhuangTai() : "unknown";
            String zuoYeShangCiZhiXingShiJian = policy.getZuoYeShangCiZhiXingShiJian() != null ? policy.getZuoYeShangCiZhiXingShiJian() : "unknown";
            String zuoYeShangCiWanChengShiJian = policy.getZuoYeShangCiWanChengShiJian() != null ? policy.getZuoYeShangCiWanChengShiJian() : "unknown";
            String zuoYeXiaCiZhiXingShiJian = policy.getZuoYeXiaCiZhiXingShiJian() != null ? policy.getZuoYeXiaCiZhiXingShiJian() : "unknown";

            // 1. 设置备份作业基本信息指标
            backupJobInfo.labels(beiFenRuanJianMingChen, beiFenZuoYeZhuangTai, zuoYeMingChen).set(1.0);

            // 2. 设置备份作业执行结果指标
            backupJobExecutionResult.labels(zuoYeMingChen, beiFenRuanJianMingChen, zuoYeShangCiZhiXingJieGuo)
                    .set(policy.getExecutionResultValue());

            // 3. 设置备份作业执行状态指标
            backupJobExecutionStatus.labels(zuoYeMingChen, beiFenRuanJianMingChen, zuoYeZhiXingZhuangTai)
                    .set(policy.getExecutionStatusValue());

            // 4. 设置备份作业状态指标
            backupJobStatus.labels(zuoYeMingChen, beiFenRuanJianMingChen, beiFenZuoYeZhuangTai)
                    .set(policy.getBackupJobStatusValue());

            // 5. 设置备份作业时间信息指标
            backupJobTimeInfo.labels(zuoYeMingChen, zuoYeShangCiZhiXingShiJian, zuoYeShangCiWanChengShiJian, zuoYeXiaCiZhiXingShiJian)
                    .set(1.0);
            
            logger.debug("Set backup job metrics for: {}", zuoYeMingChen);
        }
    }

    /**
     * 应用过滤条件
     */
    private List<PolicyExecutionData> applyFilters(List<PolicyExecutionData> policyDataList, PolicyMetricsRequestDto request) {
        return policyDataList.stream()
                .filter(policy -> request.getPolicyType() == null || request.getPolicyType().equals(policy.getBeiFenRuanJianMingChen()))
                .filter(policy -> request.getPolicyName() == null || request.getPolicyName().equals(policy.getZuoYeMingChen()))
                .filter(policy -> request.getInstanceId() == null || request.getInstanceId().equals(policy.getInstanceId()))
                .filter(policy -> request.getExecutionStatus() == null || request.getExecutionStatus().equals(policy.getZuoYeZhiXingZhuangTai()))
                .limit(request.getLimit() != null ? request.getLimit() : 100)
                .collect(Collectors.toList());
    }


}
