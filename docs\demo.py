#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
File: dws_exporter.py
Time: 2024/12/23 14:14:22
Author: Steamed_Rice
Version: Python 3.8.20
Desc: None
'''

import os
import sys
import json
import requests
import argparse
import logging
import traceback
import hashlib
import urllib3
import datetime
from flask import Flask, request, Response
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import prometheus_client
from prometheus_client import Gauge
from prometheus_client.core import CollectorRegistry
urllib3.disable_warnings()


# 定义版本号
def get_version():
    return 'Version 0.0.1, Version time 2024/12/19.'


# 定义传入的参数
description = "Welcome To Use DWS Exporter."
parser = argparse.ArgumentParser(description=description)
parser.add_argument('--host', default='0.0.0.0', help='IPv4 Listen Address.')
parser.add_argument('--port', default='16003', help='Service Listen Port.')
parser.add_argument('--version', action='version', version=get_version(), help='Application Version.')
args = parser.parse_args()

# 固定函数，不需要更改
def decrypt(private_key_str: str, message: str) -> str:
    '''
    RSA 非对称解密函数
    private_key_str: 解密私钥文本
    message: 解密文本内容
    '''
    private_key = RSA.import_key(private_key_str)
    cipher = PKCS1_OAEP.new(private_key)
    plain_text = cipher.decrypt(message).decode()

    return plain_text

# 可扩展函数，根据各个产品特性进行编写
def get_dws_login(host: str, port: int, username: str, password: str) -> str:
    '''
    获取 DWS 登录态函数
    host: DWS 集群地址
    port: DWS 集群端口
    username: DWS 登录账号
    password: DWS 登录密码
    '''

    url = f"https://{host}:{port}/rest/plat/smapp/v1/oauth/token"
    headers = {
        "Content-Type": "application/json;charset=UTF-8"
    }
    body = {
        "grantType": "password",
        "userName": username,
        "value": password
    }

    resp = requests.put(url=url, headers=headers, json=body, verify=False).json()
    token = resp["accessSession"]

    return token

# 固定函数，不需要更改
def build_api_key(path, params, api_key, api_secret):
    '''
    解析 OMP API 密钥函数
    path: 请求的接口路径
    param: 请求参数
    api_key: Api Key
    api_secret: Api Secret
    '''

    values = "".join([str(params[k]) for k in sorted((params or {}).keys())
                      if k not in ("_key", "_secret") and not isinstance(params[k], (dict, list))])
    _secret = "".join([path, api_secret, values]).encode("utf-8")
    params["_secret"] = hashlib.sha1(_secret).hexdigest()
    params["_key"] = api_key

    return params

# 可扩展函数，根据各个产品特性进行编写
def find_content(resource_text: list, search_text: str) -> dict:
    '''
    通过资源台账匹配厅局单位信息函数
    resource_text: 资源台账文本
    search_text: 匹配文本
    '''

    # 根据实例 ID 与 OMP 信息进行匹配，并返回相关的实例信息，其中 "unit_name", "system_name", "unit_contact", "unit_contact_phone", "unit_contact_mail", 
    # "technical_contact", "technical_contact_phone", "technical_contact_mail", "status", "job_number", "resource_create_time", 
    # "resource_change_time", "alert_instance_name", "instance_id" 为固定的标签，剩余其他标签可以根据产品特性自行定义。
    for text in resource_text:
        if text["SGID"] is not None and text["SGID"] in search_text:
            text_dict = {
                "unit_name": text['units_name'],
                "system_name": text['system_name'],
                "unit_contact": text['DanWeiLianXiRen'],
                "unit_contact_phone": text['DanWeiLianXiRen_Phone'],
                "unit_contact_mail": text['DanWeiLianXiRen_Mail'],
                "technical_contact": text['JiShuLianXiRen'],
                "technical_contact_phone": text['JiShuLianXiRen_Phone'],
                "technical_contact_mail": text['JiShuLianXiRen_Mail'],
                "status": text['ZhuangTai'],
                "job_number": text['JiFeiTiaoZhengDanHao'],
                "resource_create_time": text['ZiYuanChuangJianRiQi'],
                "resource_change_time": text['JiFeiTiaoZhengRiQI'],
                "project_name": text['Project'],
                "resource_set": text['ZiYuanJi'],
                "resource_name": text['JiQunMingCheng'],
                "cpu": text['CPUHe'],
                "memory": text['NeiCun_GB'],
                "capacity": text['CunChuGB'],
                "shard_num": text['FenPianShu'],
            }
            break
        else:
            text_dict = {
                "unit_name": '-',
                "system_name": '-',
                "unit_contact": '-',
                "unit_contact_phone": '-',
                "unit_contact_mail": '-',
                "technical_contact": '-',
                "technical_contact_phone": '-',
                "technical_contact_mail": '-',
                "status": '-',
                "job_number": '-',
                "resource_create_time": '-',
                "resource_change_time": '-',
                "project_name": "-",
                "resource_set": "-",
                "resource_name": "-",
                "cpu": "-",
                "memory": "-",
                "capacity": "-",
                "shard_num": "-",
            }

    return text_dict

# 可扩展函数，根据各个产品特性进行编写
def get_cluster_metrics(host: str, port: str, token: str, resource_text: str) -> str:
    '''
    获取集群指标函数
    host: DWS 集群地址
    port: DWS 集群端口
    token: DWS 登录态
    resource_text: OMP 资源信息
    '''

    # 产品集群相关指标定义，key 为采集器暴露的指标名称，value 为获取产品接口的监控指标名称
    metrics_dict = {
        "dws001_shared_buffer_hit_ratio": "dws001_shared_buffer_hit_ratio",
        "dws002_in_memory_sort_ratio": "dws002_in_memory_sort_ratio",
        "dws003_physical_reads": "dws003_physical_reads",
        "dws004_physical_writes": "dws004_physical_writes",
        "dws005_physical_reads_per_second": "dws005_physical_reads_per_second",
        "dws006_physical_writes_per_second": "dws006_physical_writes_per_second",
        "dws007_db_size": "dws007_db_size",
        "dws008_active_sql_count": "dws008_active_sql_count",
        "dws009_session_count": "dws009_session_count"
    }
    REGISTRY = CollectorRegistry(auto_describe=False)
    
    # 定义 cluster_name 的集群信息标签，其中 "unit_name", "system_name", "unit_contact", "unit_contact_phone", "unit_contact_mail", 
    # "technical_contact", "technical_contact_phone", "technical_contact_mail", "status", "job_number", "resource_create_time", 
    # "resource_change_time", "alert_instance_name", "instance_id" 为固定的标签，剩余其他标签可以根据产品特性自行定义。
    cluster_name = Gauge('cluster_name', '', ["unit_name", "system_name", "unit_contact", "unit_contact_phone", "unit_contact_mail", "technical_contact", "technical_contact_phone", "technical_contact_mail", "status", "job_number", "resource_create_time", "resource_change_time", "alert_instance_name", "instance_id", "project_name", "resource_set", "resource_name", "cpu", "memory", "capacity", "shard_num"], registry=REGISTRY)
    metrics = {}

    # 定义产品每个指标的类型和标签，其中 "alert_instance_name", "instance_id" 为固定标签，其他的可自行定义。
    for metric_key, metric_obj in metrics_dict.items():
        metrics[metric_key] = Gauge(metric_key, '', ["alert_instance_name", "instance_id"], registry=REGISTRY)
    
    # 获取产品监控指标的相关代码
    end_time = datetime.datetime.now() - datetime.timedelta(minutes=1)
    begin_time = end_time - datetime.timedelta(minutes=6)
    url = f"https://{host}:{port}/rest/analysis/v1/datasets/perf-namespace-sys-dws?pageNo=1&pageSize=1000"
    head = {
        "Content-Type": "application/json;",
        "X-Auth-Token": token
    }
    body = {
        "timeRange": {
            "beginTime": int(begin_time.timestamp() * 1000),
            "endTime": int(end_time.timestamp() * 1000)
        },
        "filters": {
        },
        "dimensions": [
            {
                "field": "dimensions.oridim.datastore_id",
                "index": 1
            }
        ],
        "metrics": []
    }
    
    body_metrics_list = []
    for metric_key, metric_obj in metrics_dict.items():
        response_body = {
            "aggType": "avg",
            "field": f"metrics.{metric_obj}"
        }
        
        body_metrics_list.append(response_body)
    body["metrics"] = body_metrics_list

    resp = requests.post(url=url, headers=head, json=body, verify=False).json()
    
    try:
        for data in resp["datas"]:
            instance_id = data["datastore_id"]
            alert_instance_name = instance_id
            info = find_content(resource_text=resource_text, search_text=instance_id)
            cluster_name.labels(unit_name=info["unit_name"], system_name=info["system_name"], unit_contact=info["unit_contact"], unit_contact_phone=info["unit_contact_phone"], unit_contact_mail=info["unit_contact_mail"], technical_contact=info["technical_contact"], technical_contact_phone=info["technical_contact_phone"], technical_contact_mail=info["technical_contact_mail"], status=info["status"], job_number=info["job_number"], resource_create_time=info["resource_create_time"], resource_change_time=info["resource_change_time"], alert_instance_name=alert_instance_name, instance_id=instance_id, project_name=info["project_name"], resource_set=info["resource_set"], resource_name=info["resource_name"], cpu=info["cpu"], memory=info["memory"], capacity=info["capacity"], shard_num=info["shard_num"]).set(float(1))
            
            for metric_key, metric_obj in metrics_dict.items():
                try:
                    metrics[metric_key].labels(alert_instance_name=alert_instance_name, instance_id=instance_id).set(float(data[metric_obj]))
                except KeyError:
                    continue
    except:
        logger.info(traceback.format_exc())
    
    return prometheus_client.generate_latest(REGISTRY).decode('utf-8')


app = Flask(__name__)
@app.after_request


# 固定接口，采集器获取监控数据接口
@app.route('/metrics', methods=['GET'])
def response_metrics():
    host = request.args.get('host', 'Guest')
    port = request.args.get('port', 'Guest')
    username = request.args.get('username', 'Guest')
    password = request.args.get('password', 'Guest')
    dws_token = get_dws_login(host=host, port=port,
                               username=username,
                               password=decrypt(private_key, bytes.fromhex(password)))

    # 加载资源台账信息
    with open("taizhang.txt", "r") as f:
        resource_text = json.loads(f.read())
    
    return_text = get_cluster_metrics(host=host, port=port, token=dws_token, resource_text=resource_text["result"])

    return Response(return_text, mimetype='text/plain'), 200


# 固定接口，更新资源台账到本地接口
@app.route('/update_taizhang', methods=['POST'])
def update_taizhang():
    domain = request.json.get('domain', 'Guest')
    path = request.json.get('path', 'Guest')
    api_key = request.json.get('api_key', 'Guest')
    api_secret = request.json.get('api_secret', 'Guest')
    url = domain + path

    # 这里的 payload 为获取不同产品OMP信息的定义，124 代表 DWS 产品，获取其他产品只需要改这个值就可以了
    payload = {
        "page": 1,
        "page_size": 999999,
        "source": {
            "type_id": 100
        },
        "target": {
            "type_ids": [
            124
            ]
        },
        "path": [
            [
            100,
            124
            ]
        ]
    }

    payload = build_api_key(path=path, params=payload, api_key=api_key,
                            api_secret=decrypt(private_key, bytes.fromhex(api_secret)))

    response = requests.post(url, json=payload, verify=False).json()

    try:
        return_list = []
        # response["paths"]["系统-DWS"] 这里也需要根据不同的产品名称进行修改，例如获取 TDSQL 的就定义成 response["paths"]["系统-TDSQL"]。
        for i in response["paths"]["系统-DWS"]:
            return_dict = {}
            return_dict.update(response["id2ci"][i[0]])
            return_dict.update(response["id2ci"][i[1]])
            return_list.append(return_dict)
        
        return_json = {"result": return_list}
        with open('taizhang.txt', "w", encoding="utf-8") as f:
            json.dump(return_json, f, ensure_ascii=False)
            f.close()
        return Response("The information has been successfully updated locally.", mimetype='text/plain'), 200
    
    except:
        return Response("The obtained information is abnormal.", mimetype='text/plain'), 500

# 固定接口，获取采集器版本号接口
@app.route('/version')
def version():
    return get_version()


if __name__ == '__main__':

    # 解密私钥，不用更改。
    private_key = '''**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''

    # 切换到采集器当前的目录
    if getattr(sys, 'frozen', False):
        os.chdir(os.path.dirname(sys.executable))
    else:
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 定义请求日志记录文件
    log_file = open('app.log', mode='a', encoding='utf-8')
    logging.basicConfig(
        stream=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    logger = logging.getLogger()

    # 程序启动函数
    app.run(host=args.host, port=args.port)
