package com.tongdao.datainterface.controller;

import com.tongdao.datainterface.config.AppConfig;
import com.tongdao.datainterface.model.dto.PolicyMetricsRequestDto;
import com.tongdao.datainterface.model.dto.ResourceSyncRequestDto;
import com.tongdao.datainterface.service.ConfigDataService;
import com.tongdao.datainterface.service.CryptoService;
import com.tongdao.datainterface.service.OmpApiService;
import com.tongdao.datainterface.service.PolicyMetricsService;
import com.tongdao.datainterface.exception.BusinessException;
import com.tongdao.datainterface.model.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 监控指标控制器
 * 实现与demo.py完全兼容的API接口
 * 包含三个核心接口：/metrics, /update_taizhang, /version
 */
@RestController
public class MetricsController {
    
    private static final Logger logger = LoggerFactory.getLogger(MetricsController.class);
    
    @Autowired
    private PolicyMetricsService policyMetricsService;
    
    @Autowired
    private ConfigDataService configDataService;
    
    @Autowired
    private CryptoService cryptoService;
    
    @Autowired
    private OmpApiService ompApiService;
    
    @Autowired
    private AppConfig.AppProperties appProperties;
    
    /**
     * 获取监控指标接口
     * 兼容demo.py中的/metrics接口
     * 支持GET请求参数和POST请求体两种方式
     * 
     * @param host 主机地址（可选，用于兼容性）
     * @param port 端口（可选，用于兼容性）
     * @param username 用户名（可选，用于兼容性）
     * @param password 密码（可选，用于兼容性）
     * @param policyType 策略类型（可选）
     * @param policyName 策略名称（可选）
     * @param instanceId 实例ID（可选）
     * @param executionStatus 执行状态（可选）
     * @return Prometheus格式的监控数据
     */
    @GetMapping(value = "/metrics", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> getMetrics(
            @RequestParam(value = "host", required = false) String host,
            @RequestParam(value = "port", required = false) String port,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "password", required = false) String password,
            @RequestParam(value = "policy_type", required = false) String policyType,
            @RequestParam(value = "policy_name", required = false) String policyName,
            @RequestParam(value = "instance_id", required = false) String instanceId,
            @RequestParam(value = "execution_status", required = false) String executionStatus,
            @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit) {
        
        try {
            logger.info("Received metrics request - host: {}, port: {}, username: {}, policy_type: {}", 
                       host, port, username, policyType);
            
            // 构建请求参数
            PolicyMetricsRequestDto request = PolicyMetricsRequestDto.builder()
                    .policyType(policyType)
                    .policyName(policyName)
                    .instanceId(instanceId)
                    .executionStatus(executionStatus)
                    .limit(limit)
                    .build();
            
            // 生成监控指标数据
            String metricsData = policyMetricsService.generatePolicyMetrics(request);
            
            logger.debug("Generated metrics data successfully, length: {}", metricsData.length());
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(metricsData);
            
        } catch (BusinessException e) {
            logger.error("Business error in metrics endpoint: {}", e.getMessage());
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Error generating metrics: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error in metrics endpoint: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Internal server error");
        }
    }
    
    /**
     * POST方式获取监控指标接口
     * 支持更复杂的请求参数
     */
    @PostMapping(value = "/metrics", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> getMetricsPost(@Valid @RequestBody PolicyMetricsRequestDto request) {
        
        try {
            logger.info("Received POST metrics request: {}", request);
            
            // 生成监控指标数据
            String metricsData = policyMetricsService.generatePolicyMetrics(request);
            
            logger.debug("Generated metrics data successfully, length: {}", metricsData.length());
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(metricsData);
            
        } catch (BusinessException e) {
            logger.error("Business error in POST metrics endpoint: {}", e.getMessage());
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Error generating metrics: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error in POST metrics endpoint: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Internal server error");
        }
    }
    
    /**
     * 更新资源台账接口
     * 兼容demo.py中的/update_taizhang接口
     * 
     * @param request 资源同步请求参数
     * @return 更新结果消息
     */
    @PostMapping(value = "/update_taizhang", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> updateTaizhang(@Valid @RequestBody ResourceSyncRequestDto request) {
        
        try {
            logger.info("Received update_taizhang request - domain: {}, path: {}", 
                       request.getDomain(), request.getPath());
            
            // 解密API密钥
            String decryptedApiSecret = null;
            if (request.getApiSecret() != null && !request.getApiSecret().trim().isEmpty()) {
                try {
                    decryptedApiSecret = cryptoService.decryptPassword(request.getApiSecret());
                } catch (Exception e) {
                    logger.warn("Failed to decrypt API secret, using original value: {}", e.getMessage());
                    decryptedApiSecret = request.getApiSecret();
                }
            }
            
            // 调用OMP API获取资源台账信息
            String resourceData = ompApiService.fetchResourceData(
                    request.getDomain(),
                    request.getPath(),
                    request.getApiKey(),
                    decryptedApiSecret
            );
            
            // 更新本地资源台账文件
            configDataService.updateResourceInfo(resourceData);
            
            logger.info("Resource info updated successfully");
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("The information has been successfully updated locally.");
            
        } catch (BusinessException e) {
            logger.error("Business error in update_taizhang endpoint: {}", e.getMessage());
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("The obtained information is abnormal.");
        } catch (Exception e) {
            logger.error("Unexpected error in update_taizhang endpoint: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("The obtained information is abnormal.");
        }
    }
    
    /**
     * 获取版本信息接口
     * 兼容demo.py中的/version接口
     * 
     * @return 版本信息字符串
     */
    @GetMapping(value = "/version", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> getVersion() {
        
        try {
            String version = String.format("Version %s, Version time %s.", 
                    appProperties.getVersion() != null ? appProperties.getVersion() : "0.0.1",
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd")));
            
            logger.debug("Returning version: {}", version);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(version);
            
        } catch (Exception e) {
            logger.error("Error getting version: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Version 0.0.1, Version time 2024/12/01.");
        }
    }
    
    /**
     * 刷新缓存接口（额外功能）
     * 用于手动刷新配置数据缓存
     */
    @PostMapping(value = "/refresh_cache", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> refreshCache() {
        
        try {
            logger.info("Received refresh cache request");
            
            configDataService.refreshCache();
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Cache refreshed successfully.");
            
        } catch (Exception e) {
            logger.error("Error refreshing cache: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("Failed to refresh cache.");
        }
    }
    
    /**
     * 健康检查接口（额外功能）
     */
    @GetMapping(value = "/health", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> health() {
        
        try {
            // 简单的健康检查
            configDataService.getPolicyExecutionData();
            configDataService.getResourceInfo();
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("OK");
            
        } catch (Exception e) {
            logger.error("Health check failed: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("FAILED");
        }
    }
}
