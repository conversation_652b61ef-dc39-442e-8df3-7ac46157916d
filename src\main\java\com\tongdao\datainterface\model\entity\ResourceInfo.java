package com.tongdao.datainterface.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 资源台账信息实体类
 * 基于demo文件中的资源台账数据结构设计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResourceInfo {
    
    // ========== 固定标签字段（来自demo文件注释） ==========
    
    /**
     * 单位名称
     */
    @JsonProperty("unit_name")
    private String unitName;
    
    /**
     * 系统名称
     */
    @JsonProperty("system_name")
    private String systemName;
    
    /**
     * 单位联系人
     */
    @JsonProperty("unit_contact")
    private String unitContact;
    
    /**
     * 单位联系人电话
     */
    @JsonProperty("unit_contact_phone")
    private String unitContactPhone;
    
    /**
     * 单位联系人邮箱
     */
    @JsonProperty("unit_contact_mail")
    private String unitContactMail;
    
    /**
     * 技术联系人
     */
    @JsonProperty("technical_contact")
    private String technicalContact;
    
    /**
     * 技术联系人电话
     */
    @JsonProperty("technical_contact_phone")
    private String technicalContactPhone;
    
    /**
     * 技术联系人邮箱
     */
    @JsonProperty("technical_contact_mail")
    private String technicalContactMail;
    
    /**
     * 状态
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 工单号
     */
    @JsonProperty("job_number")
    private String jobNumber;
    
    /**
     * 资源创建时间
     */
    @JsonProperty("resource_create_time")
    private String resourceCreateTime;
    
    /**
     * 资源变更时间
     */
    @JsonProperty("resource_change_time")
    private String resourceChangeTime;
    
    /**
     * 告警实例名称
     */
    @JsonProperty("alert_instance_name")
    private String alertInstanceName;
    
    /**
     * 实例ID
     */
    @JsonProperty("instance_id")
    private String instanceId;
    
    // ========== 产品特性字段 ==========
    
    /**
     * 项目名称
     */
    @JsonProperty("project_name")
    private String projectName;
    
    /**
     * 资源集
     */
    @JsonProperty("resource_set")
    private String resourceSet;
    
    /**
     * 资源名称/集群名称
     */
    @JsonProperty("resource_name")
    private String resourceName;
    
    /**
     * CPU核数
     */
    @JsonProperty("cpu")
    private String cpu;
    
    /**
     * 内存大小(GB)
     */
    @JsonProperty("memory")
    private String memory;
    
    /**
     * 存储容量(GB)
     */
    @JsonProperty("capacity")
    private String capacity;
    
    // ========== DWS特有字段 ==========
    
    /**
     * 分片数（DWS特有）
     */
    @JsonProperty("shard_num")
    private String shardNum;
    
    // ========== TDATA特有字段 ==========
    
    /**
     * 数据库ID（TDATA特有）
     */
    @JsonProperty("DBID")
    private String dbid;
    
    /**
     * SCAN IP（TDATA特有）
     */
    @JsonProperty("SCANIP")
    private String scanIp;
    
    /**
     * 所属区域（TDATA特有）
     */
    @JsonProperty("SuoShuQuYu")
    private String region;
    
    /**
     * 运许号（TDATA特有）
     */
    @JsonProperty("YunXuHao")
    private String yunXuHao;
}
