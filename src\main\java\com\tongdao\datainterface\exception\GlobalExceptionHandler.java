package com.tongdao.datainterface.exception;

import com.tongdao.datainterface.model.dto.ApiResponse;
import com.tongdao.datainterface.model.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理应用中的各种异常，返回标准格式的错误响应
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理自定义业务异常
     * 
     * @param ex 业务异常
     * @return 错误响应
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException ex) {
        logger.warn("Business exception occurred: code={}, message={}", ex.getCode(), ex.getMessage(), ex);
        
        ApiResponse<Object> response = ApiResponse.error(ex.getCode(), ex.getMessage());
        HttpStatus status = getHttpStatusFromCode(ex.getCode());
        
        return new ResponseEntity<>(response, status);
    }
    
    /**
     * 处理参数验证异常 - @Valid注解验证失败
     * 
     * @param ex 方法参数验证异常
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(MethodArgumentNotValidException ex) {
        logger.warn("Validation exception occurred: {}", ex.getMessage());
        
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        ApiResponse<Object> response = ApiResponse.badRequest("参数验证失败: " + errorMessage);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理绑定异常 - 表单数据绑定失败
     * 
     * @param ex 绑定异常
     * @return 错误响应
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(BindException ex) {
        logger.warn("Bind exception occurred: {}", ex.getMessage());
        
        String errorMessage = ex.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        ApiResponse<Object> response = ApiResponse.badRequest("数据绑定失败: " + errorMessage);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理约束违反异常 - Bean Validation约束违反
     * 
     * @param ex 约束违反异常
     * @return 错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleConstraintViolationException(ConstraintViolationException ex) {
        logger.warn("Constraint violation exception occurred: {}", ex.getMessage());
        
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        
        ApiResponse<Object> response = ApiResponse.badRequest("约束验证失败: " + errorMessage);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理缺少请求参数异常
     * 
     * @param ex 缺少请求参数异常
     * @return 错误响应
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingParameterException(MissingServletRequestParameterException ex) {
        logger.warn("Missing parameter exception occurred: {}", ex.getMessage());
        
        String message = String.format("缺少必需的请求参数: %s", ex.getParameterName());
        ApiResponse<Object> response = ApiResponse.badRequest(message);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理方法参数类型不匹配异常
     * 
     * @param ex 方法参数类型不匹配异常
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        logger.warn("Type mismatch exception occurred: {}", ex.getMessage());
        
        String message = String.format("参数类型不匹配: %s", ex.getName());
        ApiResponse<Object> response = ApiResponse.badRequest(message);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理HTTP消息不可读异常 - JSON格式错误
     * 
     * @param ex HTTP消息不可读异常
     * @return 错误响应
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<Object>> handleMessageNotReadableException(HttpMessageNotReadableException ex) {
        logger.warn("Message not readable exception occurred: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.badRequest("请求体格式错误，请检查JSON格式");
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理HTTP请求方法不支持异常
     * 
     * @param ex HTTP请求方法不支持异常
     * @return 错误响应
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        logger.warn("Method not supported exception occurred: {}", ex.getMessage());
        
        String message = String.format("不支持的HTTP方法: %s", ex.getMethod());
        ApiResponse<Object> response = ApiResponse.error(ErrorCode.METHOD_NOT_ALLOWED.getCode(), message);
        return new ResponseEntity<>(response, HttpStatus.METHOD_NOT_ALLOWED);
    }
    
    /**
     * 处理404异常 - 找不到处理器
     * 
     * @param ex 找不到处理器异常
     * @return 错误响应
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoHandlerFoundException(NoHandlerFoundException ex) {
        logger.warn("No handler found exception occurred: {}", ex.getMessage());
        
        String message = String.format("请求的资源不存在: %s %s", ex.getHttpMethod(), ex.getRequestURL());
        ApiResponse<Object> response = ApiResponse.notFound(message);
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }
    
    /**
     * 处理所有其他未捕获的异常
     * 
     * @param ex 异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex) {
        logger.error("Unexpected exception occurred", ex);
        
        ApiResponse<Object> response = ApiResponse.error("服务器内部错误，请稍后重试");
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 根据错误码获取对应的HTTP状态码
     * 
     * @param errorCode 错误码
     * @return HTTP状态码
     */
    private HttpStatus getHttpStatusFromCode(Integer errorCode) {
        if (errorCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        
        return switch (errorCode) {
            case 400 -> HttpStatus.BAD_REQUEST;
            case 401 -> HttpStatus.UNAUTHORIZED;
            case 403 -> HttpStatus.FORBIDDEN;
            case 404 -> HttpStatus.NOT_FOUND;
            case 405 -> HttpStatus.METHOD_NOT_ALLOWED;
            default -> {
                if (errorCode >= 400 && errorCode < 500) {
                    yield HttpStatus.BAD_REQUEST;
                } else if (errorCode >= 500) {
                    yield HttpStatus.INTERNAL_SERVER_ERROR;
                } else {
                    yield HttpStatus.OK;
                }
            }
        };
    }
}
