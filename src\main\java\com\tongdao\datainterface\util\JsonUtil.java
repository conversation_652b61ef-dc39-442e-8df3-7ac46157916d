package com.tongdao.datainterface.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON处理工具类
 * 提供JSON序列化和反序列化功能
 */
public class JsonUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonUtil.class);
    
    private static final ObjectMapper objectMapper;
    
    static {
        objectMapper = new ObjectMapper();
        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());
        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略空值属性
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }
    
    /**
     * 获取ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
    
    /**
     * 对象转JSON字符串
     * 
     * @param object 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("Object to JSON string failed: {}", e.getMessage(), e);
            throw new RuntimeException("JSON序列化失败", e);
        }
    }
    
    /**
     * 对象转格式化的JSON字符串
     * 
     * @param object 要转换的对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJsonString(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("Object to pretty JSON string failed: {}", e.getMessage(), e);
            throw new RuntimeException("JSON序列化失败", e);
        }
    }
    
    /**
     * JSON字符串转对象
     * 
     * @param jsonString JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T fromJsonString(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            logger.error("JSON string to object failed: {}", e.getMessage(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }
    
    /**
     * JSON字符串转对象（支持泛型）
     * 
     * @param jsonString JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T fromJsonString(String jsonString, TypeReference<T> typeReference) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            logger.error("JSON string to object with TypeReference failed: {}", e.getMessage(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }
    
    /**
     * JSON字符串转Map
     * 
     * @param jsonString JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> fromJsonStringToMap(String jsonString) {
        return fromJsonString(jsonString, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * JSON字符串转List
     * 
     * @param jsonString JSON字符串
     * @param elementClass 列表元素类型
     * @param <T> 泛型类型
     * @return List对象
     */
    public static <T> List<T> fromJsonStringToList(String jsonString, Class<T> elementClass) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(jsonString, 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (IOException e) {
            logger.error("JSON string to List failed: {}", e.getMessage(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        }
    }
    
    /**
     * 对象转Map
     * 
     * @param object 要转换的对象
     * @return Map对象
     */
    public static Map<String, Object> objectToMap(Object object) {
        if (object == null) {
            return null;
        }
        
        return objectMapper.convertValue(object, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Map转对象
     * 
     * @param map Map对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        
        return objectMapper.convertValue(map, clazz);
    }
    
    /**
     * 深拷贝对象
     * 
     * @param object 源对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 拷贝后的对象
     */
    public static <T> T deepCopy(Object object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        
        try {
            String jsonString = objectMapper.writeValueAsString(object);
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            logger.error("Deep copy object failed: {}", e.getMessage(), e);
            throw new RuntimeException("对象深拷贝失败", e);
        }
    }
    
    /**
     * 验证JSON字符串格式是否正确
     * 
     * @param jsonString JSON字符串
     * @return 是否为有效的JSON格式
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
