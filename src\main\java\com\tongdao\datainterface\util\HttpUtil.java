package com.tongdao.datainterface.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.util.Map;

/**
 * HTTP客户端工具类
 * 封装HTTP请求功能，支持连接池
 */
public class HttpUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    
    private static RestTemplate restTemplate;
    
    /**
     * 设置RestTemplate实例
     * 通常由Spring配置类注入
     * 
     * @param restTemplate RestTemplate实例
     */
    public static void setRestTemplate(RestTemplate restTemplate) {
        HttpUtil.restTemplate = restTemplate;
    }
    
    /**
     * 获取RestTemplate实例
     * 
     * @return RestTemplate实例
     */
    private static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            throw new IllegalStateException("RestTemplate not initialized. Please configure it in AppConfig.");
        }
        return restTemplate;
    }
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T get(String url, Class<T> responseType) {
        return get(url, null, responseType);
    }
    
    /**
     * 发送GET请求（带请求头）
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T get(String url, HttpHeaders headers, Class<T> responseType) {
        try {
            HttpEntity<Void> entity = new HttpEntity<>(headers);
            ResponseEntity<T> response = getRestTemplate().exchange(url, HttpMethod.GET, entity, responseType);
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("HTTP GET request failed: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("HTTP GET请求失败: " + e.getMessage(), e);
        } catch (ResourceAccessException e) {
            logger.error("HTTP GET request timeout or connection failed: {}", e.getMessage());
            throw new RuntimeException("HTTP GET请求超时或连接失败", e);
        }
    }
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T post(String url, Object requestBody, Class<T> responseType) {
        return post(url, requestBody, null, responseType);
    }
    
    /**
     * 发送POST请求（带请求头）
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T post(String url, Object requestBody, HttpHeaders headers, Class<T> responseType) {
        try {
            if (headers == null) {
                headers = new HttpHeaders();
            }
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<T> response = getRestTemplate().exchange(url, HttpMethod.POST, entity, responseType);
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("HTTP POST request failed: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("HTTP POST请求失败: " + e.getMessage(), e);
        } catch (ResourceAccessException e) {
            logger.error("HTTP POST request timeout or connection failed: {}", e.getMessage());
            throw new RuntimeException("HTTP POST请求超时或连接失败", e);
        }
    }
    
    /**
     * 发送PUT请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T put(String url, Object requestBody, Class<T> responseType) {
        return put(url, requestBody, null, responseType);
    }
    
    /**
     * 发送PUT请求（带请求头）
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T put(String url, Object requestBody, HttpHeaders headers, Class<T> responseType) {
        try {
            if (headers == null) {
                headers = new HttpHeaders();
            }
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<T> response = getRestTemplate().exchange(url, HttpMethod.PUT, entity, responseType);
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("HTTP PUT request failed: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("HTTP PUT请求失败: " + e.getMessage(), e);
        } catch (ResourceAccessException e) {
            logger.error("HTTP PUT request timeout or connection failed: {}", e.getMessage());
            throw new RuntimeException("HTTP PUT请求超时或连接失败", e);
        }
    }
    
    /**
     * 发送DELETE请求
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T delete(String url, Class<T> responseType) {
        return delete(url, null, responseType);
    }
    
    /**
     * 发送DELETE请求（带请求头）
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型泛型
     * @return 响应结果
     */
    public static <T> T delete(String url, HttpHeaders headers, Class<T> responseType) {
        try {
            HttpEntity<Void> entity = new HttpEntity<>(headers);
            ResponseEntity<T> response = getRestTemplate().exchange(url, HttpMethod.DELETE, entity, responseType);
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("HTTP DELETE request failed: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("HTTP DELETE请求失败: " + e.getMessage(), e);
        } catch (ResourceAccessException e) {
            logger.error("HTTP DELETE request timeout or connection failed: {}", e.getMessage());
            throw new RuntimeException("HTTP DELETE请求超时或连接失败", e);
        }
    }
    
    /**
     * 创建HTTP请求头
     * 
     * @return HttpHeaders对象
     */
    public static HttpHeaders createHeaders() {
        return new HttpHeaders();
    }
    
    /**
     * 创建带认证token的HTTP请求头
     * 
     * @param token 认证token
     * @return HttpHeaders对象
     */
    public static HttpHeaders createAuthHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        return headers;
    }
    
    /**
     * 创建带自定义认证头的HTTP请求头
     * 
     * @param headerName 认证头名称
     * @param headerValue 认证头值
     * @return HttpHeaders对象
     */
    public static HttpHeaders createCustomAuthHeaders(String headerName, String headerValue) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(headerName, headerValue);
        return headers;
    }
    
    /**
     * 添加请求头
     * 
     * @param headers HttpHeaders对象
     * @param name 头名称
     * @param value 头值
     */
    public static void addHeader(HttpHeaders headers, String name, String value) {
        headers.set(name, value);
    }
    
    /**
     * 批量添加请求头
     * 
     * @param headers HttpHeaders对象
     * @param headerMap 头映射
     */
    public static void addHeaders(HttpHeaders headers, Map<String, String> headerMap) {
        if (headerMap != null) {
            headerMap.forEach(headers::set);
        }
    }
}
