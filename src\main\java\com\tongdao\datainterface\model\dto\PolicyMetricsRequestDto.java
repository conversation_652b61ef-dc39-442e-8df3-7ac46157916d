package com.tongdao.datainterface.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

/**
 * 策略监控指标请求DTO
 * 用于接收获取策略监控指标的请求参数
 * 兼容demo.py中的参数格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PolicyMetricsRequestDto {
    
    /**
     * 策略类型（可选）
     * 用于过滤特定类型的策略
     */
    @JsonProperty("policy_type")
    private String policyType;
    
    /**
     * 策略名称（可选）
     * 用于过滤特定名称的策略
     */
    @JsonProperty("policy_name")
    private String policyName;
    
    /**
     * 实例ID（可选）
     * 用于过滤特定实例的策略
     */
    @JsonProperty("instance_id")
    private String instanceId;
    
    /**
     * 执行状态过滤（可选）
     * RUNNING, COMPLETED, FAILED, PENDING
     */
    @JsonProperty("execution_status")
    private String executionStatus;
    
    /**
     * 时间范围开始（可选）
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("start_time")
    private String startTime;
    
    /**
     * 时间范围结束（可选）
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("end_time")
    private String endTime;
    
    /**
     * 是否包含详细信息
     */
    @JsonProperty("include_details")
    @Builder.Default
    private Boolean includeDetails = false;
    
    /**
     * 最大返回数量
     */
    @JsonProperty("limit")
    @Builder.Default
    private Integer limit = 100;
    
    /**
     * 排序字段
     * last_execution_time, policy_name, execution_result
     */
    @JsonProperty("sort_by")
    @Builder.Default
    private String sortBy = "last_execution_time";
    
    /**
     * 排序方向
     * asc, desc
     */
    @JsonProperty("sort_order")
    @Builder.Default
    private String sortOrder = "desc";
}
