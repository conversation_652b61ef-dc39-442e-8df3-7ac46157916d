-- MySQL dump 10.13  Distrib 5.7.26, for Linux (x86_64)
--
-- Host: localhost    Database: cdmone_saas
-- ------------------------------------------------------
-- Server version	5.7.26-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `account_inf`
--

DROP TABLE IF EXISTS `account_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_inf` (
  `id` varchar(32) NOT NULL COMMENT '账户编号',
  `name` varchar(255) DEFAULT NULL COMMENT '账户名称',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='账户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `account_setting_inf`
--

DROP TABLE IF EXISTS `account_setting_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `account_setting_inf` (
  `id` varchar(32) NOT NULL,
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `key` varchar(64) DEFAULT NULL,
  `value` varchar(1024) DEFAULT NULL,
  `domain_id` varchar(32) DEFAULT NULL COMMENT '数据中心',
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `archive_storage_pool_inf`
--

DROP TABLE IF EXISTS `archive_storage_pool_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `archive_storage_pool_inf` (
  `id` varchar(32) NOT NULL COMMENT '归档存储池编号',
  `internal_id` varchar(50) DEFAULT NULL COMMENT '内部Id',
  `name` varchar(255) DEFAULT NULL COMMENT '归档存储池名称',
  `type` varchar(10) DEFAULT NULL COMMENT '归档存储池类型：\r\npublic：公有云存储池 \r\nnormal：普通私有云存储池',
  `descr` varchar(255) DEFAULT NULL COMMENT '说明',
  `app_id` varchar(255) DEFAULT '' COMMENT 'app_id',
  `app_secret` varchar(255) DEFAULT NULL COMMENT 'app_secret',
  `address` varchar(128) DEFAULT NULL COMMENT '地址，nas用',
  `end_point` varchar(255) DEFAULT NULL COMMENT 'end_point',
  `bucket` varchar(50) DEFAULT NULL COMMENT '存储桶',
  `sub_type` varchar(50) DEFAULT NULL COMMENT '厂商',
  `extended_info` longtext COMMENT '扩展信息',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `status` char(1) DEFAULT NULL COMMENT '0无效1有效',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '数据中心id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='归档存储池';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_agent_inf`
--

DROP TABLE IF EXISTS `backup_agent_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_agent_inf` (
  `id` varchar(32) NOT NULL DEFAULT '' COMMENT '客户端编号',
  `name` varchar(255) DEFAULT NULL COMMENT '客户端名称',
  `ip` varchar(512) DEFAULT NULL COMMENT 'IP地址',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：agent， vmware',
  `os_type` varchar(32) DEFAULT NULL COMMENT '操作系统类型：windows，linux，unix',
  `os_version` varchar(128) DEFAULT NULL COMMENT '操作系统',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 正常，2 异常，3  删除',
  `app_version` varchar(32) DEFAULT NULL COMMENT '程序版本号',
  `username` varchar(128) DEFAULT NULL COMMENT '用户名',
  `password` varchar(128) DEFAULT NULL COMMENT '密码',
  `extended_info` longtext COMMENT '扩展信息（并发数concurrent_count，限速rate_limit,卷列表volumes）',
  `is_prod` char(1) DEFAULT NULL COMMENT '是否生产环境（1是0否）',
  `transfer_protocol` varchar(255) DEFAULT NULL COMMENT '传输协议',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '区域编号',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `is_online` char(1) DEFAULT '1' COMMENT '是否启用（1是0否）',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) USING BTREE,
  KEY `idx_ip` (`ip`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户端信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_image_inf`
--

DROP TABLE IF EXISTS `backup_image_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_image_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父id',
  `type` varchar(32) DEFAULT NULL COMMENT '备份类型：fbackup，ibackup，log，restore_backup，clone，archive，copy',
  `action_type` char(32) DEFAULT NULL COMMENT '动作类型：backup，save，clone，archive，copy',
  `run_type` varchar(20) DEFAULT NULL COMMENT '执行类型：auto，manual',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `snapshot_name` varchar(256) DEFAULT NULL COMMENT '快照名称',
  `snapshot_time` bigint(20) DEFAULT NULL COMMENT '快照时间',
  `backup_agent_id` varchar(32) DEFAULT NULL COMMENT '客户端ID',
  `backup_agent_name` varchar(255) DEFAULT NULL COMMENT '客户端名称',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `backup_content` tinytext COMMENT '备份内容',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `used_capacity` bigint(20) DEFAULT NULL COMMENT '使用容量',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 正常，0 失效过期',
  `storage_pool_id` varchar(256) DEFAULT NULL COMMENT '存储池ID',
  `storage_pool_name` varchar(256) DEFAULT NULL COMMENT '存储池名称',
  `storage_controller_id` varchar(256) DEFAULT NULL COMMENT '存储控制器ID',
  `storage_controller_name` varchar(256) DEFAULT NULL COMMENT '存储控制器名称',
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `extended_info` longtext COMMENT '扩展信息：（INSTANCE_ID）',
  `policy_id` varchar(32) DEFAULT NULL COMMENT '备份计划ID',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称',
  `expire_time` bigint(20) DEFAULT NULL COMMENT '过期时间，毫秒数',
  `log_days` double DEFAULT NULL,
  `source_image_id` varchar(32) DEFAULT NULL COMMENT '来源备份镜像ID',
  `source_id` varchar(32) DEFAULT NULL COMMENT '来源ID',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `compress_size` bigint(20) DEFAULT NULL,
  `tag_id` varchar(32) DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`),
  KEY `idx_backup_image_inf` (`backup_object_id`,`action_type`,`snapshot_time`) USING BTREE,
  KEY `idx_backup_image_inf_1` (`source_id`,`source_image_id`) USING BTREE,
  KEY `idx_backup_image_inf_2` (`action_type`,`status`) USING BTREE,
  KEY `idx_backup_image_inf_3` (`source_image_id`) USING BTREE,
  KEY `idx_backup_image_inf_4` (`parent_id`) USING BTREE,
  KEY `idx_di_ct_at_pi_bai_pn_s_et_uc` (`domain_id`,`create_time`,`action_type`,`parent_id`,`backup_agent_id`,`policy_name`,`status`,`expire_time`,`used_capacity`) USING BTREE,
  KEY `idx_backup_image_inf_snapshot_name` (`snapshot_name`),
  KEY `idx_backup_image_inf_volume_name` (`volume_name`) USING BTREE,
  KEY `index_bii_20230207` (`volume_name`,`snapshot_name`) USING BTREE,
  KEY `dex_backup_image_inf_backup_object_id` (`backup_object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='备份镜像信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_image_inf_20230628`
--

DROP TABLE IF EXISTS `backup_image_inf_20230628`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_image_inf_20230628` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父id',
  `type` varchar(32) DEFAULT NULL COMMENT '备份类型：fbackup，ibackup，log，restore_backup，clone，archive，copy',
  `action_type` char(32) DEFAULT NULL COMMENT '动作类型：backup，save，clone，archive，copy',
  `run_type` varchar(20) DEFAULT NULL COMMENT '执行类型：auto，manual',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `snapshot_name` varchar(256) DEFAULT NULL COMMENT '快照名称',
  `snapshot_time` bigint(20) DEFAULT NULL COMMENT '快照时间',
  `backup_agent_id` varchar(32) DEFAULT NULL COMMENT '客户端ID',
  `backup_agent_name` varchar(255) DEFAULT NULL COMMENT '客户端名称',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `backup_content` tinytext COMMENT '备份内容',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `used_capacity` bigint(20) DEFAULT NULL COMMENT '使用容量',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 正常，0 失效过期',
  `storage_pool_id` varchar(256) DEFAULT NULL COMMENT '存储池ID',
  `storage_pool_name` varchar(256) DEFAULT NULL COMMENT '存储池名称',
  `storage_controller_id` varchar(256) DEFAULT NULL COMMENT '存储控制器ID',
  `storage_controller_name` varchar(256) DEFAULT NULL COMMENT '存储控制器名称',
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `extended_info` text COMMENT '扩展信息：（INSTANCE_ID）',
  `policy_id` varchar(32) DEFAULT NULL COMMENT '备份计划ID',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称',
  `expire_time` bigint(20) DEFAULT NULL COMMENT '过期时间，毫秒数',
  `log_days` double DEFAULT NULL,
  `source_image_id` varchar(32) DEFAULT NULL COMMENT '来源备份镜像ID',
  `source_id` varchar(32) DEFAULT NULL COMMENT '来源ID',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `tag_id` varchar(32) DEFAULT NULL COMMENT '标签ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_image_operation_param_inf`
--

DROP TABLE IF EXISTS `backup_image_operation_param_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_image_operation_param_inf` (
  `type` varchar(50) NOT NULL COMMENT '操作类型',
  `values` longtext COMMENT '参数值',
  PRIMARY KEY (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='操作备份镜像自定义参数信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_object_inf`
--

DROP TABLE IF EXISTS `backup_object_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_object_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `internal_id` varchar(128) DEFAULT NULL COMMENT '内部ID',
  `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '名称',
  `type` varchar(32) DEFAULT NULL COMMENT '类型：oracle,vmware,sqlserver,mysql,hyperv,hana,file,nas,open_data_source,distribution_data_source,db2,hdfs,volume_file,persistent_data',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 正常，2 异常，3 删除。对persistent_data而言，1是已挂载，2是未挂载',
  `display_status` varchar(32) DEFAULT NULL COMMENT '显示状态',
  `total_size` bigint(20) DEFAULT NULL COMMENT '总大小',
  `is_full_backup` varchar(1) DEFAULT NULL COMMENT '是否发起全量备份：1 是，0 否',
  `extended_info` longtext COMMENT '扩展信息',
  `backup_agent_id` varchar(50) DEFAULT NULL COMMENT '备份客户端ID',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '区域编号',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_backup_object_inf` (`type`,`backup_agent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='备份对象信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_policy_inf`
--

DROP TABLE IF EXISTS `backup_policy_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_policy_inf` (
  `id` varchar(32) NOT NULL COMMENT '备份策略编号',
  `name` varchar(255) DEFAULT NULL COMMENT '备份策略名称',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `transfer_protocol` varchar(32) DEFAULT NULL COMMENT '传输方式：iscsi，fc，ib，auto为智能选择',
  `full_cron_expression` varchar(2000) DEFAULT NULL COMMENT '全量备份计划',
  `incr_cron_expression` varchar(2000) DEFAULT NULL COMMENT '增量备份计划',
  `diff_cron_expression` varchar(100) DEFAULT NULL COMMENT '差量备份计划',
  `full_retention_time` int(5) DEFAULT NULL COMMENT '全量保留天数',
  `incr_retention_time` int(5) DEFAULT NULL COMMENT '增量保留天数',
  `diff_retention_time` int(5) DEFAULT NULL COMMENT '差量保留天数',
  `storage_pool_id` varchar(256) DEFAULT NULL COMMENT '存储池编号',
  `log_storage_pool_id` varchar(256) DEFAULT NULL COMMENT 'log存储池编号',
  `is_using` char(1) DEFAULT '1' COMMENT '是否启用：1是 0否',
  `is_dedup` char(1) DEFAULT NULL COMMENT '压缩去重 1，是 ，0否',
  `exec_type` varchar(32) DEFAULT NULL COMMENT '执行方式 always_increment；increment',
  `increment_type` varchar(32) DEFAULT NULL COMMENT '增量方式 increment(增量合成)；increment_only(仅增量)',
  `merge_type` varchar(32) DEFAULT NULL COMMENT '合成方式 always_merge；merge；schedule',
  `merge_interval_times` int(5) DEFAULT NULL COMMENT '增量多少次进行一次合成',
  `merge_weeks` int(5) DEFAULT NULL COMMENT '合成间隔周数',
  `merge_week_day` int(2) DEFAULT NULL COMMENT '周几合成1-7',
  `operate_source` varchar(8) DEFAULT NULL COMMENT '复制，归档的操作源，值为backup,clone,默认backup',
  `is_clone` char(1) DEFAULT NULL COMMENT '是否克隆',
  `clone_pool_id` varchar(32) DEFAULT NULL COMMENT '克隆存储池ID',
  `clone_days` int(5) DEFAULT NULL COMMENT '克隆保留天数',
  `is_archive` char(1) DEFAULT NULL COMMENT '是否归档',
  `archive_pool_id` varchar(32) DEFAULT NULL COMMENT '归档数据中心',
  `archive_days` int(5) DEFAULT NULL COMMENT '归档保留天数',
  `archive_exec_times` int(5) DEFAULT NULL COMMENT '归档增量合成多少次后执行一次全备',
  `is_copy` char(1) DEFAULT NULL COMMENT '是否复制',
  `copy_config_id` varchar(200) DEFAULT NULL COMMENT '复制管理id',
  `copy_days` int(5) DEFAULT NULL COMMENT '复制保留天数',
  `extended_info` text COMMENT '扩展信息',
  `secret_key` varchar(128) DEFAULT NULL COMMENT '密钥',
  `priority` int(5) DEFAULT NULL COMMENT '优先级',
  `backup_channels` int(11) DEFAULT NULL COMMENT '备份通道数',
  `channel_speed_limit` int(11) DEFAULT NULL COMMENT '每通道IO限速',
  `save_copies` int(11) DEFAULT NULL COMMENT '保留份数',
  `is_backup_log` char(1) DEFAULT NULL COMMENT '是否备份日志：1 是，0 否',
  `log_cron_expression` varchar(2000) DEFAULT NULL COMMENT '日志备份计划',
  `is_del_log` char(1) DEFAULT NULL COMMENT '日志备份后删除日志',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 启用，0 禁用',
  `log_days` int(11) DEFAULT NULL COMMENT '日志保留天数',
  `log_retention_hours` int(255) DEFAULT NULL COMMENT '日志保留小时',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '区域编号',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间，毫秒数',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `log_exclude` varchar(400) DEFAULT NULL COMMENT '日志备份排除时间',
  `diff_exclude` varchar(400) DEFAULT NULL COMMENT '差量备份排除时间',
  `clone_cron_expression` varchar(2000) DEFAULT NULL COMMENT '自动任务cron表达式',
  `is_clone_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  `is_clone_log` char(1) DEFAULT NULL COMMENT '是否本地复制log快照 1：是 0：否',
  `clone_log_cron_expression` varchar(2000) DEFAULT NULL COMMENT '自动本地复制cron表达式',
  `clone_log_pool_id` varchar(32) DEFAULT NULL COMMENT 'log本地复制存储池ID',
  `clone_log_days` int(5) DEFAULT NULL COMMENT 'log本地复制保留天数',
  `is_clone_log_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  `archive_cron_expression` varchar(2000) DEFAULT NULL COMMENT '自动归档cron表达式',
  `is_archive_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  `is_archive_log` char(1) DEFAULT NULL COMMENT '是否自动归档 1：是 0：否',
  `archive_log_cron_expression` varchar(2000) DEFAULT NULL COMMENT '自动归档cron表达式',
  `archive_log_pool_id` varchar(32) DEFAULT NULL COMMENT 'log归档目标池ID',
  `archive_log_days` int(5) DEFAULT NULL COMMENT 'log归档保留天数',
  `archive_log_exec_times` int(5) DEFAULT NULL COMMENT '归档增量合成多少次后执行一次全备',
  `is_archive_log_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  `copy_cron_expression` varchar(2000) DEFAULT NULL COMMENT '自动复制cron表达式',
  `is_copy_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  `is_copy_log` char(1) DEFAULT NULL COMMENT '是否自动复制log 1：是 0：否',
  `copy_log_cron_expression` varchar(2000) DEFAULT NULL COMMENT 'log自动复制cron表达式',
  `copy_log_config_id` varchar(200) DEFAULT NULL COMMENT 'log复制策略ID',
  `copy_log_days` int(5) DEFAULT NULL COMMENT 'log复制保留天数',
  `is_copy_log_new_data` char(1) DEFAULT NULL COMMENT '是否仅处理最新数据：1 是，0 否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='备份计划信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `backup_policy_object_ref_inf`
--

DROP TABLE IF EXISTS `backup_policy_object_ref_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `backup_policy_object_ref_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `policy_id` varchar(32) DEFAULT NULL COMMENT '备份策略ID',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `backup_content` varchar(255) DEFAULT NULL COMMENT '备份对象内容',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '数据中心',
  PRIMARY KEY (`id`),
  UNIQUE KEY `backup_object_id` (`backup_object_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='备份对象-备份策略关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `client_count_inf`
--

DROP TABLE IF EXISTS `client_count_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_count_inf` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `client_type` varchar(64) NOT NULL COMMENT '客户端类型',
  `client_count` int(255) NOT NULL COMMENT '客户端数量',
  `backup_time` bigint(20) NOT NULL COMMENT '统计时间',
  `domain_id` varchar(32) NOT NULL COMMENT 'DOMAIN_ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`backup_time`,`client_type`,`domain_id`) USING BTREE,
  KEY `idx_time` (`backup_time`)
) ENGINE=InnoDB AUTO_INCREMENT=6561 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cloud_file_storage_inf`
--

DROP TABLE IF EXISTS `cloud_file_storage_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cloud_file_storage_inf` (
  `id` varchar(64) NOT NULL,
  `name` varchar(255) DEFAULT NULL COMMENT '服务名',
  `storage_pool_id` varchar(64) DEFAULT NULL COMMENT '存储池id',
  `pool_controller_id` varchar(64) DEFAULT NULL COMMENT '存储控制器id',
  `instruction` text COMMENT 'fuse挂载指令',
  `path` varchar(255) DEFAULT NULL COMMENT '共享子目录',
  `extended_info` text COMMENT '扩展信息',
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  `account_id` varchar(32) DEFAULT NULL,
  `user_id` varchar(32) DEFAULT NULL,
  `status` char(1) DEFAULT NULL,
  `domain_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cloud_light_inf`
--

DROP TABLE IF EXISTS `cloud_light_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cloud_light_inf` (
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `brick_id` varchar(128) NOT NULL COMMENT '密钥id',
  `brick_key` varchar(128) NOT NULL COMMENT '密钥key',
  `firm_type` varchar(32) NOT NULL COMMENT '厂商类型，目前 aliyun,ctyun.ucloud',
  `last_connection_time` bigint(32) DEFAULT NULL COMMENT '最后链接时间',
  PRIMARY KEY (`brick_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contact_inf`
--

DROP TABLE IF EXISTS `contact_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contact_inf` (
  `id` varchar(32) NOT NULL,
  `name` varchar(128) DEFAULT NULL COMMENT '姓名',
  `email` varchar(128) DEFAULT NULL COMMENT '联系方式【邮件】',
  `phone` varchar(30) DEFAULT NULL COMMENT '联系方式【短信】',
  `account_id` varchar(32) DEFAULT NULL,
  `user_id` varchar(32) DEFAULT NULL,
  `domain_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='联系人信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `copy_config_inf`
--

DROP TABLE IF EXISTS `copy_config_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `copy_config_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(256) DEFAULT NULL COMMENT '复制名称',
  `source_domain_id` varchar(32) DEFAULT NULL COMMENT '源数据中心id',
  `target_domain_id` varchar(32) DEFAULT NULL COMMENT '目标数据中心id',
  `storage_pool_type` varchar(8) DEFAULT NULL COMMENT 'block   object  file',
  `target_storage_pool_id` varchar(32) DEFAULT NULL COMMENT '目标数据存储池',
  `target_archive_storage_pool_id` varchar(32) DEFAULT NULL COMMENT '目标归档存储池',
  `archive_days` int(11) DEFAULT NULL COMMENT '归档保留天数',
  `archive_exec_times` int(5) DEFAULT NULL COMMENT '归档多少次后全量',
  `clone_pool_id` varchar(32) DEFAULT NULL COMMENT '克隆池id',
  `clone_days` int(11) DEFAULT NULL COMMENT '克隆保留天数',
  `extended_info` text COMMENT '扩展信息',
  `status` char(1) DEFAULT NULL COMMENT '0无效1有效',
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `data_usage_inf`
--

DROP TABLE IF EXISTS `data_usage_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `data_usage_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父id',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `backup_object_name` varchar(256) DEFAULT NULL COMMENT '备份对象名称',
  `backup_image_id` varchar(32) DEFAULT NULL COMMENT '备份镜像ID',
  `log_backup_image_id` varchar(32) DEFAULT NULL COMMENT 'LOG备份镜像ID',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型：oracle ，vmware',
  `type` varchar(32) DEFAULT NULL COMMENT '备份类型：mount，restore？？是否需要',
  `status` char(1) DEFAULT NULL COMMENT '状态：0 进行中 1  成功， 2 失败， 3 已保存，4 已卸载',
  `target_agent_id` varchar(32) DEFAULT NULL COMMENT '目标客户端ID',
  `target_agent_name` varchar(128) DEFAULT NULL COMMENT '目标客户端名称',
  `mount_point` varchar(300) DEFAULT NULL COMMENT '挂载点',
  `transfer_protocol` varchar(32) DEFAULT NULL COMMENT '传输方式：iscsi，fc，ib',
  `transfer_protocol_value` varchar(128) DEFAULT NULL COMMENT '传输方式值',
  `policy_id` varchar(32) DEFAULT NULL COMMENT '备份计划ID',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称',
  `log_volume_name` varchar(20) DEFAULT NULL COMMENT '日志克隆卷名',
  `volume_name` varchar(256) DEFAULT NULL COMMENT '数据库克隆卷名',
  `storage_controller_id` varchar(256) DEFAULT NULL COMMENT '存储控制器id',
  `storage_controller_name` varchar(256) DEFAULT NULL COMMENT '存储控制器名称',
  `extended_info` text COMMENT '扩展信息（数据内容）',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间，毫秒数',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `tag_id` varchar(32) DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据使用记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `db_backup_record_inf`
--

DROP TABLE IF EXISTS `db_backup_record_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `db_backup_record_inf` (
  `id` varchar(32) NOT NULL,
  `type` varchar(20) DEFAULT NULL COMMENT 'director 或者 saas',
  `file_name` varchar(50) DEFAULT NULL COMMENT '文件名',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `domain_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='db备份记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `del_inf`
--

DROP TABLE IF EXISTS `del_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `del_inf` (
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `del_inf_20240222`
--

DROP TABLE IF EXISTS `del_inf_20240222`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `del_inf_20240222` (
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `del_inf_20240304`
--

DROP TABLE IF EXISTS `del_inf_20240304`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `del_inf_20240304` (
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `policy_name` varchar(128) DEFAULT NULL COMMENT '备份计划名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `director_inf`
--

DROP TABLE IF EXISTS `director_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `director_inf` (
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `brick_id` varchar(100) NOT NULL COMMENT '密钥ID',
  `brick_key` varchar(100) DEFAULT NULL COMMENT '密钥',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账户编号',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '区域编号',
  `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
  `mac` varchar(20) DEFAULT NULL COMMENT 'mac地址',
  `version` varchar(32) DEFAULT NULL COMMENT '版本号',
  `last_connection_time` bigint(20) DEFAULT NULL COMMENT '最后链接时间',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`brick_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='master信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `domain_inf`
--

DROP TABLE IF EXISTS `domain_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `domain_inf` (
  `id` varchar(32) NOT NULL COMMENT '区域编号',
  `name` varchar(255) DEFAULT NULL COMMENT '区域名称',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='备份区域信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `job_derive_info`
--

DROP TABLE IF EXISTS `job_derive_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_derive_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `job_id` varchar(32) NOT NULL DEFAULT '' COMMENT '任务id',
  `job_name` varchar(256) NOT NULL DEFAULT '' COMMENT '任务名称',
  `policy_id` varchar(32) NOT NULL DEFAULT '' COMMENT '策略id',
  `policy_name` varchar(256) NOT NULL DEFAULT '' COMMENT '策略名称',
  `data_object_id` varchar(32) NOT NULL DEFAULT '' COMMENT '数据对象id',
  `data_object_name` varchar(128) NOT NULL DEFAULT '' COMMENT '数据对象名称',
  `data_object_type` varchar(32) NOT NULL DEFAULT '' COMMENT '数据对象类型',
  `data_object_category` varchar(32) NOT NULL DEFAULT '' COMMENT '数据对象类目',
  `client_id` varchar(32) NOT NULL DEFAULT '' COMMENT '客户端id',
  `client_type` varchar(32) NOT NULL DEFAULT '' COMMENT '客户端类型',
  `client_name` varchar(128) NOT NULL DEFAULT '' COMMENT '客户端名称',
  `storage_pool_id` varchar(256) NOT NULL DEFAULT '' COMMENT '存储池id',
  `storage_pool_name` varchar(256) NOT NULL DEFAULT '' COMMENT '存储池名称',
  `domain_id` varchar(32) NOT NULL DEFAULT '' COMMENT '数据中心id',
  `parent_id` varchar(32) NOT NULL DEFAULT '' COMMENT '父任务id',
  `action_type` varchar(32) NOT NULL DEFAULT '' COMMENT '任务动作类型',
  `action_subtype` varchar(32) NOT NULL DEFAULT '' COMMENT '任务动作子类型',
  `born_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '生成时间',
  `begin_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `cost_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '花费时间',
  `data_size` bigint(20) NOT NULL DEFAULT '0' COMMENT '数据量',
  `job_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务状态',
  `data_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据状态',
  `month_code` int(11) NOT NULL DEFAULT '0' COMMENT '月',
  `day_code` int(11) NOT NULL DEFAULT '0' COMMENT '日',
  `week_code` tinyint(4) NOT NULL DEFAULT '0' COMMENT '周',
  `index_column1` int(11) NOT NULL DEFAULT '0' COMMENT '预留索引字段1',
  `index_column2` bigint(20) NOT NULL DEFAULT '0' COMMENT '预留索引字段2',
  `index_column3` varchar(32) NOT NULL DEFAULT '' COMMENT '预留索引字段3',
  `index_column4` varchar(128) NOT NULL DEFAULT '' COMMENT '预留索引字段4',
  `extra_info` varchar(1024) NOT NULL DEFAULT '' COMMENT '额外信息',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_di_doc_bt_ct_ds_as_js` (`domain_id`,`data_object_category`,`born_time`,`cost_time`,`data_size`,`action_subtype`,`job_status`) USING BTREE,
  KEY `idx_di_as_bt_ct_ds_doc_js` (`domain_id`,`action_subtype`,`born_time`,`cost_time`,`data_size`,`data_object_category`,`job_status`) USING BTREE,
  KEY `idx_di_js_bt_cs_ds_as_doc` (`domain_id`,`job_status`,`born_time`,`cost_time`,`data_size`,`action_subtype`,`data_object_category`) USING BTREE,
  KEY `idx_di_at_bt_mc_js` (`domain_id`,`action_type`,`born_time`,`month_code`,`job_status`) USING BTREE,
  KEY `idx_di_ct_bt_dc_ct` (`domain_id`,`client_type`,`born_time`,`day_code`,`cost_time`) USING BTREE,
  KEY `idx_di_dot_bt_mc_dc_wc_ct_ds` (`domain_id`,`data_object_type`,`born_time`,`month_code`,`day_code`,`week_code`,`cost_time`,`data_size`) USING BTREE,
  KEY `idx_di_bt_doi_don_dot_doc_ci_cn_at_pi_ct_ds_ds_mc_dc_wc` (`domain_id`,`born_time`,`data_object_id`,`data_object_name`,`data_object_type`,`data_object_category`,`client_id`,`client_name`,`action_type`,`parent_id`,`cost_time`,`data_size`,`data_status`,`month_code`,`day_code`,`week_code`) USING BTREE,
  KEY `idx_di_bt_et_don_dot_doc_ci_cn_at_as_pi_bt_ct_ds_js_mc` (`domain_id`,`born_time`,`end_time`,`data_object_name`,`data_object_type`,`data_object_category`,`client_id`,`client_name`,`action_type`,`action_subtype`,`parent_id`,`begin_time`,`cost_time`,`data_size`,`job_status`,`month_code`) USING BTREE,
  KEY `idx_di_et_bt_dot` (`domain_id`,`end_time`,`born_time`,`data_object_type`) USING BTREE,
  KEY `idx_di_ct_bt_dot` (`domain_id`,`cost_time`,`born_time`,`data_object_type`) USING BTREE,
  KEY `idx_di_ds_bt_dot` (`domain_id`,`data_size`,`born_time`,`data_object_type`) USING BTREE,
  FULLTEXT KEY `idx_policy_name` (`policy_name`),
  FULLTEXT KEY `idx_data_object_name` (`data_object_name`),
  FULLTEXT KEY `idx_client_name` (`client_name`),
  FULLTEXT KEY `idx_storage_pool_name` (`storage_pool_name`)
) ENGINE=InnoDB AUTO_INCREMENT=4462326 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务宽表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `job_inf`
--

DROP TABLE IF EXISTS `job_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父id',
  `name` varchar(256) DEFAULT NULL COMMENT '名称',
  `backup_agent_id` varchar(32) DEFAULT NULL COMMENT '客户端ID',
  `backup_agent_name` varchar(128) DEFAULT NULL COMMENT '客户端名称',
  `backup_type` varchar(32) DEFAULT NULL COMMENT '作业类型：fbackup，ibackup，log',
  `action_type` varchar(32) DEFAULT NULL COMMENT '动作类型：backup，discover，mount，restore，umount，clone，archive，save_image，delete_image',
  `run_type` varchar(20) DEFAULT NULL COMMENT '执行类型：auto，manual',
  `service_id` varchar(32) DEFAULT NULL COMMENT '业务ID：备份、数据使用',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型：oracle ，vmware，agent，storage',
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `storage_pool_id` varchar(256) DEFAULT NULL COMMENT '存储池ID',
  `storage_pool_name` varchar(256) DEFAULT NULL COMMENT '存储池名',
  `policy_name` varchar(256) DEFAULT NULL COMMENT '策略名称',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `content` tinytext COMMENT '任务内容',
  `used_capacity` bigint(11) DEFAULT NULL COMMENT '使用容量',
  `finish_rate` int(11) DEFAULT NULL COMMENT '进度',
  `status` varchar(2) DEFAULT NULL COMMENT '状态：1 成功，2 失败，3 运行中  4警告  5队列中 6合成中',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `begin_time` bigint(20) DEFAULT NULL COMMENT '开始时间，毫秒数',
  `position` varchar(20) DEFAULT NULL COMMENT '当前所属位置：init_storage,agent,release_storage',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间，毫秒数',
  `error_message` text COMMENT '错误信息',
  `error_code` varchar(20) DEFAULT NULL COMMENT '错误码',
  `extended_info` longtext COMMENT '扩展信息',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_job_status` (`status`) USING BTREE,
  KEY `idx_job_inf` (`action_type`,`service_type`,`begin_time`,`domain_id`,`account_id`) USING BTREE,
  KEY `index_job_inf_20220907_1` (`id`,`begin_time`,`end_time`,`status`),
  KEY `index_job_inf_20240206_1` (`backup_type`,`action_type`),
  KEY `idx_job_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `job_inf_history`
--

DROP TABLE IF EXISTS `job_inf_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_inf_history` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父id',
  `name` varchar(256) DEFAULT NULL COMMENT '名称',
  `backup_agent_id` varchar(32) DEFAULT NULL COMMENT '客户端ID',
  `backup_agent_name` varchar(128) DEFAULT NULL COMMENT '客户端名称',
  `backup_type` varchar(32) DEFAULT NULL COMMENT '作业类型：fbackup，ibackup，log',
  `action_type` varchar(32) DEFAULT NULL COMMENT '动作类型：backup，discover，mount，restore，umount，clone，archive，save_image，delete_image',
  `run_type` varchar(20) DEFAULT NULL COMMENT '执行类型：auto，manual',
  `service_id` varchar(32) DEFAULT NULL COMMENT '业务ID：备份、数据使用',
  `service_type` varchar(32) DEFAULT NULL COMMENT '数据类型：oracle ，vmware，agent，storage',
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名',
  `storage_pool_id` varchar(256) DEFAULT NULL COMMENT '存储池ID',
  `storage_pool_name` varchar(256) DEFAULT NULL COMMENT '存储池名',
  `policy_name` varchar(256) DEFAULT NULL COMMENT '策略名称',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '备份对象ID',
  `content` tinytext COMMENT '任务内容',
  `used_capacity` bigint(11) DEFAULT NULL COMMENT '使用容量',
  `finish_rate` int(11) DEFAULT NULL COMMENT '进度',
  `status` varchar(2) DEFAULT NULL COMMENT '状态：1 成功，2 失败，3 运行中  4警告  5队列中 6合成中',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `begin_time` bigint(20) DEFAULT NULL COMMENT '开始时间，毫秒数',
  `position` varchar(20) DEFAULT NULL COMMENT '当前所属位置：init_storage,agent,release_storage',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间，毫秒数',
  `error_message` text COMMENT '错误信息',
  `error_code` varchar(20) DEFAULT NULL COMMENT '错误码',
  `extended_info` longtext COMMENT '扩展信息',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `sync_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_job_status` (`status`) USING BTREE,
  KEY `idx_job_inf` (`action_type`,`service_type`,`begin_time`,`domain_id`,`account_id`) USING BTREE,
  KEY `idx_job_inf2` (`create_time`,`status`) USING BTREE,
  KEY `idx_job_inf3` (`begin_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='作业历史';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `job_running_process_inf`
--

DROP TABLE IF EXISTS `job_running_process_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_running_process_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `job_id` varchar(32) DEFAULT NULL COMMENT '作业ID',
  `action` varchar(32) DEFAULT NULL COMMENT '动作：init，backup，save_image，mount，restore，unmount，clone，copy，discover',
  `target` varchar(20) DEFAULT NULL COMMENT '目标：agent，storage，vmware',
  `target_id` varchar(50) DEFAULT NULL COMMENT '目标标识',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 成功，0 失败',
  `type` varchar(32) DEFAULT NULL COMMENT '类型：send，receive',
  `content` longtext COMMENT '请求信息',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  PRIMARY KEY (`id`),
  KEY `idx_job_running_process_inf` (`job_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业运行过程';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `license_inf`
--

DROP TABLE IF EXISTS `license_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `license_inf` (
  `id` varchar(32) NOT NULL,
  `content` varchar(2048) DEFAULT NULL COMMENT '内容',
  `account_id` varchar(32) DEFAULT NULL,
  `domain_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='授权信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logic_storage_pool_group_inf`
--

DROP TABLE IF EXISTS `logic_storage_pool_group_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `logic_storage_pool_group_inf` (
  `id` varchar(32) NOT NULL COMMENT '存储池组ID，以group-开头，后面加26位uuid',
  `name` varchar(255) NOT NULL COMMENT '存储池组名称',
  `type` varchar(255) NOT NULL COMMENT '存储池类型，block、file、object、offline',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间，毫秒数',
  `extended_info` text COMMENT '扩展信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logic_storage_pool_group_ref_inf`
--

DROP TABLE IF EXISTS `logic_storage_pool_group_ref_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `logic_storage_pool_group_ref_inf` (
  `id` varchar(32) NOT NULL,
  `logic_storage_pool_id` varchar(32) NOT NULL COMMENT '逻辑存储池ID',
  `logic_storage_pool_group_id` varchar(32) NOT NULL COMMENT '存储池组ID',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池组关联关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logic_storage_pool_inf`
--

DROP TABLE IF EXISTS `logic_storage_pool_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `logic_storage_pool_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(256) DEFAULT NULL COMMENT '名称',
  `type` varchar(32) DEFAULT NULL COMMENT '类型',
  `sub_type` varchar(50) DEFAULT NULL COMMENT '厂商：aliyun，azure，aws，jdcloud，tecent，ctyun',
  `is_encrypt` char(1) DEFAULT '0' COMMENT '是否加密：1 是，0 否',
  `encrypt_algorithm` varchar(50) DEFAULT '' COMMENT '加密算法',
  `is_compress` char(1) DEFAULT '0' COMMENT '是否压缩：1 是，0 否',
  `compress_algorithm` varchar(50) DEFAULT '' COMMENT '压缩算法',
  `is_dedup` char(1) DEFAULT '0' COMMENT '是否重删：1 是，0 否',
  `dedup_port` varchar(50) DEFAULT '' COMMENT '重删端口',
  `storage_pool_id` varchar(32) DEFAULT NULL COMMENT '存储资源ID',
  `status` char(1) DEFAULT NULL COMMENT '0无效1有效',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间，毫秒数',
  `extended_info` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='逻辑存储池';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `login_log_inf`
--

DROP TABLE IF EXISTS `login_log_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_log_inf` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `login_time` bigint(20) NOT NULL COMMENT '登录时间',
  `login_ip` varchar(32) NOT NULL COMMENT '登录ip',
  `login_user_id` varchar(255) DEFAULT NULL COMMENT '登陆用户id',
  `login_result` char(1) NOT NULL DEFAULT '1' COMMENT '登陆结果，1成功2失败',
  PRIMARY KEY (`id`),
  KEY `idx_userId` (`login_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11713 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mail_policy_inf`
--

DROP TABLE IF EXISTS `mail_policy_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mail_policy_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(256) DEFAULT NULL COMMENT '邮件策略名',
  `recipient` longtext COMMENT '收件人',
  `alarm_type` varchar(128) DEFAULT NULL COMMENT '系统通知',
  `backup_policy_id` longtext COMMENT '备份策略名',
  `backup_policy_all` varchar(2) DEFAULT NULL COMMENT '默认选中所有策略',
  `backup_notice_type` varchar(255) DEFAULT NULL COMMENT '备份策略通知类型',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '数据中心id',
  `notice_level` varchar(255) DEFAULT NULL COMMENT '备份策略通知级别',
  `alarm_level` varchar(255) DEFAULT NULL COMMENT '系统通知级别',
  `action_type` varchar(255) DEFAULT NULL COMMENT '作业失败的类型',
  `user_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `menu_inf`
--

DROP TABLE IF EXISTS `menu_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `menu_inf` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '菜单编号',
  `parent_id` bigint(11) NOT NULL COMMENT '父级菜单编号',
  `name` varchar(256) DEFAULT NULL COMMENT '菜单名称',
  `path` varchar(256) DEFAULT NULL COMMENT '菜单路径',
  `icon` varchar(256) DEFAULT NULL COMMENT '菜单图标',
  `type` char(1) DEFAULT NULL COMMENT '菜单类型 \r\n0目录1菜单2按钮',
  `perms` varchar(512) DEFAULT NULL COMMENT '所需要的权限',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `order` int(4) DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8 COMMENT='菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `report_custom_inf`
--

DROP TABLE IF EXISTS `report_custom_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `report_custom_inf` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '自定义报表名称',
  `description` varchar(255) DEFAULT NULL COMMENT '报表描述',
  `status` char(1) DEFAULT NULL COMMENT '0：创建中 1：完成 2：失败',
  `sql_script` longtext COMMENT 'sql脚本',
  `content` longtext COMMENT 'sql执行结果',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
  `create_user` varchar(32) DEFAULT NULL COMMENT '创建者',
  `update_user` varchar(32) DEFAULT NULL COMMENT '修改者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `report_inf`
--

DROP TABLE IF EXISTS `report_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `report_inf` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '报表名称',
  `description` varchar(255) DEFAULT NULL COMMENT '报表描述',
  `content` text COMMENT '报表配置信息',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
  `create_user` varchar(32) DEFAULT NULL COMMENT '创建者',
  `update_user` varchar(32) DEFAULT NULL COMMENT '修改者',
  `sort_num` int(4) NOT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role_inf`
--

DROP TABLE IF EXISTS `role_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_inf` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '角色编号',
  `name` varchar(256) DEFAULT NULL COMMENT '角色名称',
  `account_id` varchar(32) DEFAULT NULL COMMENT '租户id',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1001 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role_menu_ref_inf`
--

DROP TABLE IF EXISTS `role_menu_ref_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_menu_ref_inf` (
  `role_id` bigint(11) NOT NULL COMMENT '角色编号',
  `menu_id` bigint(11) NOT NULL COMMENT '菜单编号',
  UNIQUE KEY `idx_unique` (`role_id`,`menu_id`) USING BTREE COMMENT '角色菜单唯一对应'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色权限关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `service_record`
--

DROP TABLE IF EXISTS `service_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `service_record` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `type` varchar(32) DEFAULT NULL COMMENT '操作类型：',
  `content` longtext COMMENT '内容',
  `is_push` char(1) DEFAULT NULL COMMENT '是否推送',
  `target` varchar(50) DEFAULT NULL COMMENT '目标对象',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_record` (`create_time`,`is_push`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务推送记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `statistics_inf`
--

DROP TABLE IF EXISTS `statistics_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `statistics_inf` (
  `策略名` varchar(128) DEFAULT NULL COMMENT '备份计划名称',
  `备份集保护级别` text,
  `数量` text,
  `备份最新时间` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_pool_controller_inf`
--

DROP TABLE IF EXISTS `storage_pool_controller_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_pool_controller_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `ip` varchar(256) DEFAULT NULL COMMENT 'IP',
  `name` varchar(256) DEFAULT NULL COMMENT '名称',
  `transfer_protocol` varchar(3000) DEFAULT NULL COMMENT '传输协议：[{type:iscsi | fc | ib,value:123}]',
  `os_version` varchar(128) DEFAULT NULL COMMENT '操作系统',
  `app_version` varchar(32) DEFAULT NULL COMMENT '程序版本',
  `replication_ip` varchar(256) DEFAULT NULL COMMENT '复制IP',
  `status` char(1) DEFAULT NULL COMMENT '状态：1  正常，2 异常，3  删除',
  `extended_info` text COMMENT '扩展信息',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间，毫秒数',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间，毫秒数',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储控制器';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_pool_controller_ref_inf`
--

DROP TABLE IF EXISTS `storage_pool_controller_ref_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_pool_controller_ref_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `storage_pool_id` varchar(32) DEFAULT NULL COMMENT '存储池ID',
  `storage_controller_id` varchar(32) DEFAULT NULL COMMENT '存储控制器ID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniquesc` (`storage_pool_id`,`storage_controller_id`) COMMENT '二者组合唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池-存储控制器关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_pool_inf`
--

DROP TABLE IF EXISTS `storage_pool_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_pool_inf` (
  `id` varchar(32) NOT NULL COMMENT '存储池编号',
  `internal_name` varchar(256) DEFAULT NULL COMMENT '存储内部名称',
  `name` varchar(256) DEFAULT NULL COMMENT '存储池名称',
  `type` varchar(10) DEFAULT NULL COMMENT '类型：zfs | zfs_ceph',
  `sla` varchar(100) DEFAULT NULL COMMENT 'SLA',
  `descr` varchar(256) DEFAULT NULL COMMENT '说明',
  `replication_ip` varchar(50) DEFAULT NULL COMMENT '复制网络IP',
  `total_size` bigint(20) DEFAULT NULL COMMENT '总大小',
  `available_size` bigint(20) DEFAULT NULL COMMENT '剩余大小',
  `is_dup` char(1) DEFAULT NULL COMMENT '是否重删',
  `is_gzip` char(1) DEFAULT NULL COMMENT '是否压缩',
  `extended_info` text COMMENT '扩展信息',
  `domain_id` varchar(32) DEFAULT NULL COMMENT '区域编号',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT NULL,
  `status` char(1) DEFAULT NULL COMMENT '状态：1 正常 ，2 失效 3删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_view_inf`
--

DROP TABLE IF EXISTS `storage_view_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_view_inf` (
  `id` varchar(32) NOT NULL COMMENT '视图ID',
  `storage_pool_id` varchar(32) DEFAULT NULL COMMENT '存储池ID',
  `status` varchar(2) DEFAULT NULL COMMENT '视图状态',
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'domain id',
  `account_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储资源透视图记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_view_snapshot_inf`
--

DROP TABLE IF EXISTS `storage_view_snapshot_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_view_snapshot_inf` (
  `id` varchar(32) NOT NULL COMMENT '快照信息ID',
  `volume_id` varchar(32) DEFAULT NULL COMMENT '所属卷ID',
  `view_id` varchar(32) DEFAULT NULL COMMENT '所属视图ID',
  `internal_name` varchar(255) DEFAULT NULL,
  `backup_image_id` varchar(32) DEFAULT NULL COMMENT '快照ID',
  `snapshot_name` varchar(255) DEFAULT NULL COMMENT '快照名',
  `size` bigint(20) DEFAULT NULL COMMENT '快照大小',
  `backup_type` varchar(32) DEFAULT NULL COMMENT '备份类型',
  `snapshot_time` bigint(20) DEFAULT NULL COMMENT '快照创建时间',
  `expire_time` bigint(20) DEFAULT NULL COMMENT '快照过期时间',
  `job_id` varchar(32) DEFAULT NULL COMMENT '关联的任务ID',
  `backup_policy_id` varchar(32) DEFAULT NULL COMMENT '关联的策略ID',
  `backup_policy_name` varchar(512) DEFAULT NULL,
  `status` varchar(2) DEFAULT NULL COMMENT '使用情况 1使用中 0未使用',
  `can_delete` char(1) DEFAULT NULL COMMENT '是否可删 0不可删 1可删',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'domain id',
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  `account_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池上的快照信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `storage_view_volume_inf`
--

DROP TABLE IF EXISTS `storage_view_volume_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `storage_view_volume_inf` (
  `id` varchar(32) NOT NULL COMMENT '卷信息ID',
  `view_id` varchar(32) DEFAULT NULL COMMENT '所属视图ID',
  `volume_name` varchar(1024) DEFAULT NULL COMMENT '卷名',
  `size` bigint(20) DEFAULT NULL COMMENT '卷大小',
  `storage_type` varchar(32) DEFAULT NULL COMMENT '存储属性 volume | filesystem',
  `backup_object_id` varchar(32) DEFAULT NULL COMMENT '数据对象',
  `service_type` varchar(64) DEFAULT NULL COMMENT '数据类型',
  `volume_create_time` bigint(20) DEFAULT NULL COMMENT '卷创建时间',
  `storage_image_count` int(11) DEFAULT NULL COMMENT '存储快照数量',
  `local_image_count` int(11) DEFAULT NULL COMMENT 'engine快照数量',
  `status` varchar(2) DEFAULT NULL COMMENT '使用情况 使用中1 未使用0',
  `can_delete` char(1) DEFAULT NULL COMMENT '是否可删 0不可删 1可删',
  `source_image_id` varchar(32) DEFAULT NULL COMMENT '源快照ID',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'domain id',
  `create_time` bigint(20) DEFAULT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  `account_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='存储池上的卷信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_dict_inf`
--

DROP TABLE IF EXISTS `sys_dict_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_dict_inf` (
  `id` varchar(32) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '名称',
  `type_code` varchar(255) NOT NULL COMMENT '类型编码',
  `dict_code` varchar(255) DEFAULT NULL COMMENT '字典编码',
  `order_no` int(10) DEFAULT NULL COMMENT '排序字段',
  `descr` varchar(1000) DEFAULT NULL COMMENT '描述',
  `status` char(1) DEFAULT NULL COMMENT '状态：1 有效，0 失效',
  PRIMARY KEY (`id`),
  KEY `idx_type_code` (`type_code`,`dict_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统字典表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_log_inf`
--

DROP TABLE IF EXISTS `sys_log_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_log_inf` (
  `id` varchar(32) NOT NULL,
  `component` varchar(20) DEFAULT NULL COMMENT '组件：storage 存储，agent  客户端 ， engine 存储引擎，director 服务层，orch 编排插件',
  `module` varchar(20) DEFAULT NULL COMMENT '功能模块：用户 user，角色 role，权限 permission，菜单menu 授权 license，存储 storage，元数据 catelog， 参数设置 setting，复制 copy_config，客户端 agent，备份对象 backup_object，策略 policy，持久化存储 persistent_data，备份快照 backup_image，数据使用 data_usage',
  `level` varchar(10) DEFAULT NULL COMMENT 'info,error,warn',
  `action_type` varchar(30) DEFAULT NULL COMMENT '登录 login，新增 add，修改 modify，删除 delete，检测 check，备份 backup，挂载 mount，卸载 umount，恢复 restore，保存挂载 save_mount，保存卸载 save_umount',
  `action_ip` varchar(25) DEFAULT NULL COMMENT '操作客户端ip',
  `content` text COMMENT '内容',
  `is_read` varchar(1) DEFAULT '0' COMMENT '阅读状态：1 已读，0 未读',
  `domain_id` varchar(32) NOT NULL,
  `account_id` varchar(32) DEFAULT NULL,
  `user_id` varchar(32) DEFAULT NULL,
  `create_time` bigint(20) NOT NULL,
  `delete_flag` varchar(1) DEFAULT '0' COMMENT '状态：1 已删除，0 未删除',
  PRIMARY KEY (`id`),
  KEY `idx_action_type` (`action_type`) USING BTREE,
  KEY `idx_compo` (`component`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tag`
--

DROP TABLE IF EXISTS `tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tag` (
  `id` varchar(32) NOT NULL COMMENT 'id',
  `name` varchar(125) NOT NULL COMMENT '名称',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT '' COMMENT '用户id',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tape_view_inf`
--

DROP TABLE IF EXISTS `tape_view_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tape_view_inf` (
  `id` varchar(32) NOT NULL DEFAULT '' COMMENT '记录ID',
  `internal_id` varchar(255) DEFAULT NULL COMMENT '磁带ID',
  `type` varchar(255) DEFAULT '' COMMENT '磁带类型',
  `barcode` varchar(255) DEFAULT NULL,
  `location` varchar(11) DEFAULT '' COMMENT '插槽位置',
  `storage_pool_id` varchar(32) DEFAULT NULL COMMENT '所属磁带库ID',
  `status` varchar(255) DEFAULT NULL COMMENT '状态',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `free_size` bigint(20) DEFAULT NULL COMMENT '剩余可使用空间',
  `capacity` bigint(20) DEFAULT NULL COMMENT '总容量',
  `used_size` bigint(20) DEFAULT NULL COMMENT '已用容量',
  `expired_size` bigint(20) DEFAULT NULL COMMENT '过期容量',
  `last_write_time` varchar(125) DEFAULT NULL,
  `reserve_time` bigint(20) DEFAULT NULL COMMENT '保留时间',
  `extended_info` text COMMENT '扩展信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_inf`
--

DROP TABLE IF EXISTS `task_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `action` varchar(256) DEFAULT NULL COMMENT '操作类型',
  `params` longtext COMMENT '操作参数',
  `status` char(1) DEFAULT NULL COMMENT '0：创建 1：已分发 2：已确认',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT '' COMMENT '用户id',
  `domain_id` varchar(32) DEFAULT NULL COMMENT 'DOMAIN_ID',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`) USING BTREE,
  KEY `index_task_inf_20220907_1` (`id`,`status`,`account_id`,`domain_id`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='操作信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `template_inf`
--

DROP TABLE IF EXISTS `template_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_inf` (
  `id` varchar(32) NOT NULL DEFAULT '' COMMENT 'id',
  `key` text COMMENT 'key',
  `value` longtext COMMENT 'value',
  `type` varchar(32) DEFAULT '' COMMENT '模板管理的类型',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账号编号',
  `user_id` varchar(32) DEFAULT '' COMMENT '用户id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_backup_agent_inf`
--

DROP TABLE IF EXISTS `user_backup_agent_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_backup_agent_inf` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户编号',
  `backup_agent_id` varchar(32) NOT NULL COMMENT '客户端编号',
  `update_priv` char(1) DEFAULT '0' COMMENT '修改权限',
  `delete_priv` char(1) DEFAULT '0' COMMENT '删除权限',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户授权客户端表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_inf`
--

DROP TABLE IF EXISTS `user_inf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_inf` (
  `id` varchar(32) NOT NULL COMMENT '用户编号',
  `account_id` varchar(32) DEFAULT NULL COMMENT '账户编号',
  `role_id` bigint(11) DEFAULT NULL COMMENT '角色编号',
  `name` varchar(255) DEFAULT NULL COMMENT '用户名称',
  `account_name` varchar(255) DEFAULT NULL COMMENT '账户名称',
  `phone` varchar(255) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(255) DEFAULT NULL COMMENT '邮件',
  `password` varchar(255) DEFAULT NULL COMMENT '登陆密码',
  `status` varchar(1) DEFAULT NULL COMMENT '状态 0：禁用,1启用,2删除',
  `token` varchar(511) DEFAULT NULL COMMENT '登陆token',
  `privacy` varchar(1) DEFAULT '0' COMMENT '隐私',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `last_login_time` bigint(20) DEFAULT NULL COMMENT '上次登陆使劲',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Temporary table structure for view `view_job_inf`
--

DROP TABLE IF EXISTS `view_job_inf`;
/*!50001 DROP VIEW IF EXISTS `view_job_inf`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `view_job_inf` AS SELECT 
 1 AS `id`,
 1 AS `parent_id`,
 1 AS `name`,
 1 AS `backup_agent_id`,
 1 AS `backup_agent_name`,
 1 AS `backup_type`,
 1 AS `action_type`,
 1 AS `run_type`,
 1 AS `service_id`,
 1 AS `service_type`,
 1 AS `volume_name`,
 1 AS `storage_pool_id`,
 1 AS `storage_pool_name`,
 1 AS `policy_name`,
 1 AS `backup_object_id`,
 1 AS `content`,
 1 AS `used_capacity`,
 1 AS `finish_rate`,
 1 AS `status`,
 1 AS `create_time`,
 1 AS `begin_time`,
 1 AS `position`,
 1 AS `end_time`,
 1 AS `error_message`,
 1 AS `error_code`,
 1 AS `extended_info`,
 1 AS `domain_id`,
 1 AS `account_id`,
 1 AS `user_id`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `vol_0612`
--

DROP TABLE IF EXISTS `vol_0612`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vol_0612` (
  `volume_name` varchar(256) DEFAULT NULL COMMENT '卷名'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Final view structure for view `view_job_inf`
--

/*!50001 DROP VIEW IF EXISTS `view_job_inf`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8 */;
/*!50001 SET character_set_results     = utf8 */;
/*!50001 SET collation_connection      = utf8_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`cdmone`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `view_job_inf` AS select `job_inf`.`id` AS `id`,`job_inf`.`parent_id` AS `parent_id`,`job_inf`.`name` AS `name`,`job_inf`.`backup_agent_id` AS `backup_agent_id`,`job_inf`.`backup_agent_name` AS `backup_agent_name`,`job_inf`.`backup_type` AS `backup_type`,`job_inf`.`action_type` AS `action_type`,`job_inf`.`run_type` AS `run_type`,`job_inf`.`service_id` AS `service_id`,`job_inf`.`service_type` AS `service_type`,`job_inf`.`volume_name` AS `volume_name`,`job_inf`.`storage_pool_id` AS `storage_pool_id`,`job_inf`.`storage_pool_name` AS `storage_pool_name`,`job_inf`.`policy_name` AS `policy_name`,`job_inf`.`backup_object_id` AS `backup_object_id`,`job_inf`.`content` AS `content`,`job_inf`.`used_capacity` AS `used_capacity`,`job_inf`.`finish_rate` AS `finish_rate`,`job_inf`.`status` AS `status`,`job_inf`.`create_time` AS `create_time`,`job_inf`.`begin_time` AS `begin_time`,`job_inf`.`position` AS `position`,`job_inf`.`end_time` AS `end_time`,`job_inf`.`error_message` AS `error_message`,`job_inf`.`error_code` AS `error_code`,`job_inf`.`extended_info` AS `extended_info`,`job_inf`.`domain_id` AS `domain_id`,`job_inf`.`account_id` AS `account_id`,`job_inf`.`user_id` AS `user_id` from `job_inf` union all select `job_inf_history`.`id` AS `id`,`job_inf_history`.`parent_id` AS `parent_id`,`job_inf_history`.`name` AS `name`,`job_inf_history`.`backup_agent_id` AS `backup_agent_id`,`job_inf_history`.`backup_agent_name` AS `backup_agent_name`,`job_inf_history`.`backup_type` AS `backup_type`,`job_inf_history`.`action_type` AS `action_type`,`job_inf_history`.`run_type` AS `run_type`,`job_inf_history`.`service_id` AS `service_id`,`job_inf_history`.`service_type` AS `service_type`,`job_inf_history`.`volume_name` AS `volume_name`,`job_inf_history`.`storage_pool_id` AS `storage_pool_id`,`job_inf_history`.`storage_pool_name` AS `storage_pool_name`,`job_inf_history`.`policy_name` AS `policy_name`,`job_inf_history`.`backup_object_id` AS `backup_object_id`,`job_inf_history`.`content` AS `content`,`job_inf_history`.`used_capacity` AS `used_capacity`,`job_inf_history`.`finish_rate` AS `finish_rate`,`job_inf_history`.`status` AS `status`,`job_inf_history`.`create_time` AS `create_time`,`job_inf_history`.`begin_time` AS `begin_time`,`job_inf_history`.`position` AS `position`,`job_inf_history`.`end_time` AS `end_time`,`job_inf_history`.`error_message` AS `error_message`,`job_inf_history`.`error_code` AS `error_code`,`job_inf_history`.`extended_info` AS `extended_info`,`job_inf_history`.`domain_id` AS `domain_id`,`job_inf_history`.`account_id` AS `account_id`,`job_inf_history`.`user_id` AS `user_id` from `job_inf_history` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-10 11:17:30
