package com.tongdao.datainterface.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 集群信息实体类
 * 用于封装集群的基本信息和关联的监控指标
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClusterInfo {
    
    /**
     * 集群名称
     */
    @JsonProperty("cluster_name")
    private String clusterName;
    
    /**
     * 数据库名称
     */
    @JsonProperty("db_name")
    private String dbName;
    
    /**
     * 实例ID
     */
    @JsonProperty("instance_id")
    private String instanceId;
    
    /**
     * 告警实例名称
     */
    @JsonProperty("alert_instance_name")
    private String alertInstanceName;
    
    /**
     * 产品类型
     */
    @JsonProperty("product_type")
    private String productType;
    
    /**
     * 产品版本
     */
    @JsonProperty("version")
    private String version;
    
    /**
     * 运行状态
     */
    @JsonProperty("running_status")
    private String runningStatus;
    
    /**
     * 主机地址
     */
    @JsonProperty("host")
    private String host;
    
    /**
     * 端口
     */
    @JsonProperty("port")
    private Integer port;
    
    /**
     * 用户名
     */
    @JsonProperty("username")
    private String username;
    
    /**
     * 资源台账信息
     */
    @JsonProperty("resource_info")
    private ResourceInfo resourceInfo;
    
    /**
     * 监控指标数据列表
     */
    @JsonProperty("metrics")
    private List<MetricData> metrics;
    
    /**
     * 最后更新时间
     */
    @JsonProperty("last_update_time")
    private LocalDateTime lastUpdateTime;
    
    /**
     * 数据收集时间
     */
    @JsonProperty("collect_time")
    private LocalDateTime collectTime;
    
    /**
     * 是否在线
     */
    @JsonProperty("is_online")
    private Boolean isOnline;
    
    /**
     * 备注信息
     */
    @JsonProperty("remarks")
    private String remarks;
}
