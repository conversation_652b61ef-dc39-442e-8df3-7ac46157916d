package com.tongdao.datainterface.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.tongdao.datainterface.exception.BusinessException;
import com.tongdao.datainterface.model.entity.PolicyExecutionData;

import com.tongdao.datainterface.model.entity.ResourceInfo;
import com.tongdao.datainterface.model.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 配置数据服务类
 * 负责从配置文件中读取策略执行数据和资源台账信息
 * 兼容demo.py中的数据加载方式
 */
@Service
public class ConfigDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigDataService.class);
    
    private final ObjectMapper objectMapper;
    
    // 配置文件路径
    private static final String POLICY_DATA_FILE = "policy_data.json";
    private static final String RESOURCE_INFO_FILE = "taizhang.txt";
    
    // 缓存数据
    private List<PolicyExecutionData> cachedPolicyData;
    private List<ResourceInfo> cachedResourceInfo;
    private long lastPolicyDataUpdate = 0;
    private long lastResourceInfoUpdate = 0;
    
    // 缓存有效期（毫秒）
    private static final long CACHE_VALIDITY_PERIOD = 60000; // 1分钟
    
    public ConfigDataService() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        // 配置忽略未知属性，避免反序列化错误
        this.objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    @PostConstruct
    public void init() {
        logger.info("Initializing ConfigDataService...");
        createDefaultConfigFiles();
    }
    
    /**
     * 获取策略执行数据
     * 支持缓存机制，避免频繁读取文件
     */
    public List<PolicyExecutionData> getPolicyExecutionData() {
        long currentTime = System.currentTimeMillis();
        
        if (cachedPolicyData == null || (currentTime - lastPolicyDataUpdate) > CACHE_VALIDITY_PERIOD) {
            logger.debug("Refreshing policy execution data cache");
            cachedPolicyData = loadPolicyExecutionData();
            lastPolicyDataUpdate = currentTime;
        }
        
        return new ArrayList<>(cachedPolicyData);
    }
    

    
    /**
     * 获取资源台账信息
     * 兼容demo.py中的taizhang.txt文件格式
     */
    public List<ResourceInfo> getResourceInfo() {
        long currentTime = System.currentTimeMillis();
        
        if (cachedResourceInfo == null || (currentTime - lastResourceInfoUpdate) > CACHE_VALIDITY_PERIOD) {
            logger.debug("Refreshing resource info cache");
            cachedResourceInfo = loadResourceInfo();
            lastResourceInfoUpdate = currentTime;
        }
        
        return new ArrayList<>(cachedResourceInfo);
    }
    
    /**
     * 从配置文件加载策略执行数据
     */
    private List<PolicyExecutionData> loadPolicyExecutionData() {
        try {
            Path filePath = Paths.get(POLICY_DATA_FILE);
            
            if (!Files.exists(filePath)) {
                logger.warn("Policy data file not found: {}, creating default data", POLICY_DATA_FILE);
                return createDefaultPolicyData();
            }
            
            String content = Files.readString(filePath);
            List<PolicyExecutionData> data = objectMapper.readValue(content, new TypeReference<List<PolicyExecutionData>>() {});
            
            logger.info("Loaded {} policy execution records from {}", data.size(), POLICY_DATA_FILE);
            return data;
            
        } catch (IOException e) {
            logger.error("Failed to load policy execution data: {}", e.getMessage(), e);
            return createDefaultPolicyData();
        }
    }
    

    
    /**
     * 从配置文件加载资源台账信息
     * 兼容demo.py中的taizhang.txt格式
     */
    private List<ResourceInfo> loadResourceInfo() {
        try {
            Path filePath = Paths.get(RESOURCE_INFO_FILE);
            
            if (!Files.exists(filePath)) {
                logger.warn("Resource info file not found: {}, creating default data", RESOURCE_INFO_FILE);
                return createDefaultResourceInfo();
            }
            
            String content = Files.readString(filePath);
            
            // 解析JSON格式的资源台账文件
            Map<String, Object> jsonData = objectMapper.readValue(content, Map.class);
            List<Map<String, Object>> resultList = (List<Map<String, Object>>) jsonData.get("result");
            
            if (resultList == null) {
                logger.warn("No 'result' field found in resource info file");
                return createDefaultResourceInfo();
            }
            
            List<ResourceInfo> resourceInfoList = new ArrayList<>();
            for (Map<String, Object> item : resultList) {
                ResourceInfo resourceInfo = convertMapToResourceInfo(item);
                resourceInfoList.add(resourceInfo);
            }
            
            logger.info("Loaded {} resource info records from {}", resourceInfoList.size(), RESOURCE_INFO_FILE);
            return resourceInfoList;
            
        } catch (IOException e) {
            logger.error("Failed to load resource info: {}", e.getMessage(), e);
            return createDefaultResourceInfo();
        }
    }
    
    /**
     * 将Map转换为ResourceInfo对象
     */
    private ResourceInfo convertMapToResourceInfo(Map<String, Object> item) {
        return ResourceInfo.builder()
                .instanceId(getStringValue(item, "instance_id", "policy_" + System.currentTimeMillis()))
                .unitName(getStringValue(item, "units_name", "默认单位"))
                .systemName(getStringValue(item, "system_name", "策略执行系统"))
                .unitContact(getStringValue(item, "DanWeiLianXiRen", "系统管理员"))
                .unitContactPhone(getStringValue(item, "DanWeiLianXiRen_Phone", "000-0000-0000"))
                .unitContactMail(getStringValue(item, "DanWeiLianXiRen_Mail", "<EMAIL>"))
                .technicalContact(getStringValue(item, "JiShuLianXiRen", "技术负责人"))
                .technicalContactPhone(getStringValue(item, "JiShuLianXiRen_Phone", "000-0000-0000"))
                .technicalContactMail(getStringValue(item, "JiShuLianXiRen_Mail", "<EMAIL>"))
                .status(getStringValue(item, "ZhuangTai", "运行中"))
                .jobNumber(getStringValue(item, "JiFeiTiaoZhengDanHao", "-"))
                .resourceCreateTime(getStringValue(item, "ZiYuanChuangJianRiQi", "2024-01-01"))
                .resourceChangeTime(getStringValue(item, "JiFeiTiaoZhengRiQI", "2024-01-01"))
                .projectName(getStringValue(item, "Project", "策略管理项目"))
                .resourceSet(getStringValue(item, "ZiYuanJi", "策略执行集群"))
                .resourceName(getStringValue(item, "JiQunMingCheng", "策略执行服务"))
                .build();
    }
    
    /**
     * 安全获取Map中的字符串值
     */
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 创建默认的策略执行数据
     */
    private List<PolicyExecutionData> createDefaultPolicyData() {
        List<PolicyExecutionData> defaultData = new ArrayList<>();
        
        LocalDateTime now = LocalDateTime.now();
        
        // 创建示例策略数据
        defaultData.add(PolicyExecutionData.builder()
                .beiFenRuanJianMingChen("NBU")
                .beiFenZuoYeZhuangTai("已启用")
                .zuoYeMingChen("数据备份策略")
                .zuoYeShangCiWanChengShiJian("2024-12-15 10:30:00")
                .zuoYeShangCiZhiXingJieGuo("成功")
                .zuoYeShangCiZhiXingShiJian("2024-12-15 09:30:00")
                .zuoYeXiaCiZhiXingShiJian("2024-12-16 09:30:00")
                .zuoYeZhiXingZhuangTai("就绪")
                .createTime(now.minusDays(7))
                .updateTime(now.minusHours(1))
                .build());
        
        defaultData.add(PolicyExecutionData.builder()
                .beiFenRuanJianMingChen("TSM")
                .beiFenZuoYeZhuangTai("已启用")
                .zuoYeMingChen("日志清理策略")
                .zuoYeShangCiWanChengShiJian("2024-12-15 08:30:00")
                .zuoYeShangCiZhiXingJieGuo("成功")
                .zuoYeShangCiZhiXingShiJian("2024-12-15 08:00:00")
                .zuoYeXiaCiZhiXingShiJian("2024-12-15 20:00:00")
                .zuoYeZhiXingZhuangTai("就绪")
                .createTime(now.minusDays(5))
                .updateTime(now.minusMinutes(30))
                .build());
        
        defaultData.add(PolicyExecutionData.builder()
                .beiFenRuanJianMingChen("*")
                .beiFenZuoYeZhuangTai("已启用")
                .zuoYeMingChen("系统监控策略")
                .zuoYeShangCiWanChengShiJian("")
                .zuoYeShangCiZhiXingJieGuo("失败")
                .zuoYeShangCiZhiXingShiJian("2024-12-15 06:00:00")
                .zuoYeXiaCiZhiXingShiJian("2024-12-15 18:00:00")
                .zuoYeZhiXingZhuangTai("失败")
                .errorMessage("Connection timeout after 60 seconds")
                .createTime(now.minusDays(3))
                .updateTime(now.minusMinutes(5))
                .build());
        
        logger.info("Created {} default policy execution records", defaultData.size());
        return defaultData;
    }
    

    
    /**
     * 创建默认的资源台账信息
     */
    private List<ResourceInfo> createDefaultResourceInfo() {
        List<ResourceInfo> defaultData = new ArrayList<>();
        
        defaultData.add(ResourceInfo.builder()
                .instanceId("policy_001")
                .unitName("信息技术部")
                .systemName("策略管理系统")
                .unitContact("张三")
                .unitContactPhone("138-0000-0001")
                .unitContactMail("<EMAIL>")
                .technicalContact("李四")
                .technicalContactPhone("138-0000-0002")
                .technicalContactMail("<EMAIL>")
                .status("运行中")
                .jobNumber("JOB-2024-001")
                .resourceCreateTime("2024-01-01 10:00:00")
                .resourceChangeTime("2024-12-01 15:30:00")
                .projectName("数据管理平台")
                .resourceSet("生产环境")
                .resourceName("策略执行服务器")
                .build());
        
        logger.info("Created {} default resource info records", defaultData.size());
        return defaultData;
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfigFiles() {
        try {
            // 创建默认策略数据文件
            Path policyDataPath = Paths.get(POLICY_DATA_FILE);
            if (!Files.exists(policyDataPath)) {
                List<PolicyExecutionData> defaultPolicyData = createDefaultPolicyData();
                String policyDataJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(defaultPolicyData);
                Files.writeString(policyDataPath, policyDataJson);
                logger.info("Created default policy data file: {}", POLICY_DATA_FILE);
            }
            

            
            // 创建默认资源台账文件
            Path resourceInfoPath = Paths.get(RESOURCE_INFO_FILE);
            if (!Files.exists(resourceInfoPath)) {
                List<ResourceInfo> defaultResourceInfo = createDefaultResourceInfo();
                Map<String, Object> resourceData = Map.of("result", defaultResourceInfo);
                String resourceDataJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(resourceData);
                Files.writeString(resourceInfoPath, resourceDataJson);
                logger.info("Created default resource info file: {}", RESOURCE_INFO_FILE);
            }
            
        } catch (IOException e) {
            logger.error("Failed to create default config files: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 刷新缓存
     */
    public void refreshCache() {
        logger.info("Manually refreshing data cache");
        cachedPolicyData = null;
        cachedResourceInfo = null;
        lastPolicyDataUpdate = 0;
        lastResourceInfoUpdate = 0;
    }
    
    /**
     * 更新资源台账信息
     * 兼容demo.py中的update_taizhang接口
     */
    public void updateResourceInfo(String jsonData) {
        try {
            Path filePath = Paths.get(RESOURCE_INFO_FILE);
            Files.writeString(filePath, jsonData);
            
            // 刷新缓存
            cachedResourceInfo = null;
            lastResourceInfoUpdate = 0;
            
            logger.info("Resource info updated successfully");
            
        } catch (IOException e) {
            logger.error("Failed to update resource info: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "更新资源台账失败: " + e.getMessage());
        }
    }
}
