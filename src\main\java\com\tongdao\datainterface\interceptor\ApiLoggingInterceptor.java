package com.tongdao.datainterface.interceptor;

import com.tongdao.datainterface.filter.CachedBodyHttpServletRequest;
import com.tongdao.datainterface.service.ApiLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingResponseWrapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * API日志记录拦截器
 * 拦截所有API请求，记录请求和响应的详细信息
 */
@Component
public class ApiLoggingInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiLoggingInterceptor.class);
    
    /**
     * 请求ID属性名
     */
    private static final String REQUEST_ID_ATTRIBUTE = "REQUEST_ID";
    
    /**
     * 请求开始时间属性名
     */
    private static final String REQUEST_START_TIME_ATTRIBUTE = "REQUEST_START_TIME";
    
    @Autowired
    private ApiLogService apiLogService;
    
    /**
     * 请求处理前的拦截
     * 生成请求ID，记录请求开始信息
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 生成唯一请求ID
        String requestId = generateRequestId();
        LocalDateTime startTime = LocalDateTime.now();

        // 将请求ID和开始时间存储到请求属性中
        request.setAttribute(REQUEST_ID_ATTRIBUTE, requestId);
        request.setAttribute(REQUEST_START_TIME_ATTRIBUTE, startTime);

        // 在preHandle阶段，请求体可能还没有被读取，所以不记录body
        // 只记录基本的请求信息，body将在afterCompletion中记录
        try {
            apiLogService.logRequestStart(requestId, request, null);
        } catch (Exception e) {
            logger.error("Failed to log request start", e);
        }

        return true;
    }
    
    /**
     * 请求处理完成后的拦截
     * 记录响应信息和处理耗时
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                              Object handler, Exception ex) throws Exception {
        
        // 获取请求ID和开始时间
        String requestId = (String) request.getAttribute(REQUEST_ID_ATTRIBUTE);
        LocalDateTime startTime = (LocalDateTime) request.getAttribute(REQUEST_START_TIME_ATTRIBUTE);
        
        if (requestId == null || startTime == null) {
            logger.warn("Request ID or start time not found in request attributes");
            return;
        }
        
        // 获取请求体内容
        String requestBody = null;
        if (request instanceof CachedBodyHttpServletRequest) {
            CachedBodyHttpServletRequest cachedRequest = (CachedBodyHttpServletRequest) request;
            requestBody = cachedRequest.getBody();
            logger.debug("Retrieved request body from CachedBodyHttpServletRequest, length: {}",
                        requestBody != null ? requestBody.length() : 0);
        } else {
            // 如果不是我们的自定义包装器，请求体可能不可用
            logger.debug("Request is not CachedBodyHttpServletRequest, request body may not be available");
        }
        
        // 获取响应体内容
        String responseBody = null;
        if (response instanceof ContentCachingResponseWrapper) {
            ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) response;
            byte[] content = wrapper.getContentAsByteArray();
            if (content.length > 0) {
                responseBody = new String(content, StandardCharsets.UTF_8);
            }
        }
        
        // 记录请求完成
        try {
            apiLogService.logRequestComplete(requestId, request, response, 
                                           requestBody, responseBody, startTime, ex);
        } catch (Exception e) {
            logger.error("Failed to log request completion", e);
        }
    }
    
    /**
     * 生成请求ID
     * 格式：时间戳-UUID前8位
     */
    private String generateRequestId() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        long timestamp = System.currentTimeMillis();
        return timestamp + "-" + uuid.substring(0, 8);
    }
}
