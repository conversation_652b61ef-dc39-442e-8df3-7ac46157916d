package com.tongdao.datainterface.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingResponseWrapper;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 内容缓存过滤器
 * 包装HttpServletRequest和HttpServletResponse，使其支持多次读取请求体和响应体
 */
@Component
@Order(1) // 确保在其他过滤器之前执行
public class ContentCachingFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(ContentCachingFilter.class);
    
    /**
     * 需要记录日志的路径前缀
     */
    private static final String[] LOG_PATH_PREFIXES = {
        "/metrics", "/update_taizhang", "/version", "/health", "/refresh_cache"
    };
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        if (request instanceof HttpServletRequest && response instanceof HttpServletResponse) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            
            // 检查是否需要记录日志
            if (shouldLogRequest(httpRequest)) {
                HttpServletRequest wrappedRequest = httpRequest;
                ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(httpResponse);

                // 对于有请求体的请求，使用我们的自定义包装器
                if (hasRequestBody(httpRequest)) {
                    try {
                        wrappedRequest = new CachedBodyHttpServletRequest(httpRequest);
                        logger.debug("Wrapped request with CachedBodyHttpServletRequest for {}", httpRequest.getRequestURI());
                    } catch (IOException e) {
                        logger.warn("Failed to wrap request with CachedBodyHttpServletRequest: {}", e.getMessage());
                        // 如果包装失败，使用原始请求
                        wrappedRequest = httpRequest;
                    }
                }

                try {
                    // 继续过滤器链
                    chain.doFilter(wrappedRequest, wrappedResponse);
                } finally {
                    // 确保响应内容被写回到原始响应中
                    wrappedResponse.copyBodyToResponse();
                }
            } else {
                // 不需要记录日志的请求，直接传递
                chain.doFilter(request, response);
            }
        } else {
            // 非HTTP请求，直接传递
            chain.doFilter(request, response);
        }
    }
    
    /**
     * 判断请求是否有请求体
     *
     * @param request HTTP请求对象
     * @return 是否有请求体
     */
    private boolean hasRequestBody(HttpServletRequest request) {
        String method = request.getMethod();
        return "POST".equalsIgnoreCase(method) ||
               "PUT".equalsIgnoreCase(method) ||
               "PATCH".equalsIgnoreCase(method);
    }

    /**
     * 判断是否需要记录请求日志
     *
     * @param request HTTP请求对象
     * @return 是否需要记录日志
     */
    private boolean shouldLogRequest(HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 排除静态资源和健康检查等不需要记录的请求
        if (requestURI.contains("/actuator/") ||
            requestURI.contains("/static/") ||
            requestURI.contains("/css/") ||
            requestURI.contains("/js/") ||
            requestURI.contains("/images/") ||
            requestURI.contains("/favicon.ico")) {
            return false;
        }

        // 检查是否匹配需要记录日志的路径
        for (String prefix : LOG_PATH_PREFIXES) {
            if (requestURI.startsWith(prefix)) {
                return true;
            }
        }

        // 默认记录所有API请求（以/api开头的请求）
        return requestURI.startsWith("/api/");
    }
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("ContentCachingFilter initialized");
    }
    
    @Override
    public void destroy() {
        logger.info("ContentCachingFilter destroyed");
    }
}
