package com.tongdao.datainterface.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 策略执行数据实体类
 * 用于存储策略执行的相关信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PolicyExecutionData {
    
    /**
     * 备份软件名称
     */
    @JsonProperty("BeiFenRuanJianMingChen")
    private String beiFenRuanJianMingChen;
    
    /**
     * 备份作业状态
     */
    @JsonProperty("BeiFenZuoYeZhuangTai")
    private String beiFenZuoYeZhuangTai;
    
    /**
     * 作业名称
     */
    @JsonProperty("ZuoYeMingChen")
    private String zuoYeMingChen;
    
    /**
     * 作业上次完成时间
     */
    @JsonProperty("ZuoYeShangCiWanChengShiJian")
    private String zuoYeShangCiWanChengShiJian;
    
    /**
     * 作业上次执行结果
     */
    @JsonProperty("ZuoYeShangCiZhiXingJieGuo")
    private String zuoYeShangCiZhiXingJieGuo;
    
    /**
     * 作业上次执行时间
     */
    @JsonProperty("ZuoYeShangCiZhiXingShiJian")
    private String zuoYeShangCiZhiXingShiJian;
    
    /**
     * 作业下次执行时间
     */
    @JsonProperty("ZuoYeXiaCiZhiXingShiJian")
    private String zuoYeXiaCiZhiXingShiJian;
    
    /**
     * 作业执行状态
     */
    @JsonProperty("ZuoYeZhiXingZhuangTai")
    private String zuoYeZhiXingZhuangTai;
    
    /**
     * 错误信息（如果执行失败）
     */
    @JsonProperty("error_message")
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 获取告警实例名称
     * 格式：备份软件_作业名称
     */
    @JsonIgnore
    public String getAlertInstanceName() {
        if (beiFenRuanJianMingChen != null && zuoYeMingChen != null) {
            return beiFenRuanJianMingChen + "_" + zuoYeMingChen;
        }
        return zuoYeMingChen != null ? zuoYeMingChen : "unknown_job";
    }
    
    /**
     * 获取实例ID
     */
    @JsonIgnore
    public String getInstanceId() {
        return zuoYeMingChen != null ? zuoYeMingChen : "unknown_job";
    }
    
    /**
     * 获取执行结果的数值表示（用于Prometheus指标）
     */
    @JsonIgnore
    public double getExecutionResultValue() {
        if (zuoYeShangCiZhiXingJieGuo == null) {
            return -1.0; // 未知状态
        }
        
        switch (zuoYeShangCiZhiXingJieGuo) {
            case "成功":
                return 0.0;
            case "失败":
                return 1.0;
            case "部分成功":
                return 2.0;
            default:
                return -1.0;
        }
    }
    
    /**
     * 获取执行状态的数值表示（用于Prometheus指标）
     */
    @JsonIgnore
    public double getExecutionStatusValue() {
        if (zuoYeZhiXingZhuangTai == null) {
            return -1.0;
        }
        
        switch (zuoYeZhiXingZhuangTai) {
            case "就绪":
                return 0.0;
            case "运行中":
                return 1.0;
            case "已完成":
                return 2.0;
            case "失败":
                return 3.0;
            case "等待中":
                return 4.0;
            default:
                return -1.0;
        }
    }
    
    /**
     * 获取备份作业状态的数值表示（用于Prometheus指标）
     */
    @JsonIgnore
    public double getBackupJobStatusValue() {
        if (beiFenZuoYeZhuangTai == null) {
            return -1.0;
        }
        
        switch (beiFenZuoYeZhuangTai) {
            case "已启用":
                return 1.0;
            case "已禁用":
                return 0.0;
            default:
                return -1.0;
        }
    }
    
    /**
     * 获取备份软件名称（处理通配符）
     */
    @JsonIgnore
    public String getBackupSoftwareForMetrics() {
        if (beiFenRuanJianMingChen == null) {
            return "unknown";
        }
        return beiFenRuanJianMingChen.equals("*") ? "all" : beiFenRuanJianMingChen;
    }
}
