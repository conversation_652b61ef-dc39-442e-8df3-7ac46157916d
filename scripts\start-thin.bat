@echo off
REM Thin JAR Launcher Script

set JAVA_OPTS=-Xms512m -Xmx1024m
set JAR_FILE=target\dataInterface-0.0.1-SNAPSHOT-thin.jar
set LIB_DIR=target\lib

if not exist "%JAR_FILE%" (
    echo Error: JAR file not found: %JAR_FILE%
    echo Please run: mvn package -DskipTests -P thin-jar
    pause
    exit /b 1
)

if not exist "%LIB_DIR%" (
    echo Error: Lib directory not found: %LIB_DIR%
    echo Please run: mvn package -DskipTests -P thin-jar
    pause
    exit /b 1
)

echo ========================================
echo   DataInterface Thin JAR Launcher
echo ========================================
echo JAR File: %JAR_FILE%
echo Lib Dir:  %LIB_DIR%
echo Java Opts: %JAVA_OPTS%
echo ========================================

for %%F in ("%JAR_FILE%") do set JAR_SIZE=%%~zF
set /a JAR_SIZE_KB=%JAR_SIZE%/1024
echo Application JAR: %JAR_SIZE_KB% KB

echo Starting application...
java %JAVA_OPTS% -cp "%JAR_FILE%;%LIB_DIR%\*" com.tongdao.datainterface.DataInterfaceApplication
