# 分层JAR的Docker构建文件
# 利用分层特性优化Docker镜像缓存

FROM openjdk:17-jre-slim as builder
WORKDIR application
ARG JAR_FILE=target/dataInterface-0.0.1-SNAPSHOT.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted

FROM openjdk:17-jre-slim
WORKDIR application

# 按分层顺序复制，优化Docker缓存
# 依赖层（变化最少）
COPY --from=builder application/extracted/dependencies/ ./
# Spring Boot加载器层
COPY --from=builder application/extracted/spring-boot-loader/ ./
# 快照依赖层
COPY --from=builder application/extracted/snapshot-dependencies/ ./
# 应用层（变化最频繁）
COPY --from=builder application/extracted/application/ ./

EXPOSE 8080
ENTRYPOINT ["java", "org.springframework.boot.loader.launch.JarLauncher"]
