package com.tongdao.datainterface.model.enums;

/**
 * 产品类型枚举
 * 基于demo文件中的产品类型定义
 */
public enum ProductType {
    /**
     * DWS数据仓库服务
     */
    DWS("DWS", "数据仓库服务", 124),
    
    /**
     * TDATA数据库
     */
    TDATA("TDATA", "TDATA数据库", 111);

    private final String code;
    private final String description;
    private final Integer typeId;

    ProductType(String code, String description, Integer typeId) {
        this.code = code;
        this.description = description;
        this.typeId = typeId;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public Integer getTypeId() {
        return typeId;
    }

    /**
     * 根据代码获取产品类型
     */
    public static ProductType fromCode(String code) {
        for (ProductType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown product type code: " + code);
    }

    /**
     * 根据类型ID获取产品类型
     */
    public static ProductType fromTypeId(Integer typeId) {
        for (ProductType type : values()) {
            if (type.getTypeId().equals(typeId)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown product type ID: " + typeId);
    }
}
