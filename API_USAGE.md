# 策略执行监控API使用说明

## 概述

本项目实现了与Python demo文件完全兼容的策略执行监控API接口，支持Prometheus格式的监控数据输出。

## 核心API接口

### 1. 获取监控指标 - `/metrics`

**接口说明**：获取策略执行的Prometheus格式监控数据

**请求方式**：GET / POST

**GET请求示例**：
```bash
curl -X GET "http://localhost:8080/metrics"
curl -X GET "http://localhost:8080/metrics?policy_type=backup&limit=50"
```

**POST请求示例**：
```bash
curl -X POST "http://localhost:8080/metrics" \
  -H "Content-Type: application/json" \
  -d '{
    "policy_type": "backup",
    "policy_name": "数据备份策略",
    "execution_status": "COMPLETED",
    "limit": 100
  }'
```

**请求参数**：
- `policy_type` (可选): 策略类型过滤 (backup, cleanup, monitor等)
- `policy_name` (可选): 策略名称过滤
- `instance_id` (可选): 实例ID过滤
- `execution_status` (可选): 执行状态过滤 (RUNNING, COMPLETED, FAILED, PENDING)
- `limit` (可选): 最大返回数量，默认100

**返回格式**：Prometheus格式的监控数据
```
# HELP policy_execution_result Policy execution result (0=success, 1=failed, 2=partial)
# TYPE policy_execution_result gauge
policy_execution_result{alert_instance_name="backup_数据备份策略",instance_id="policy_001",policy_name="数据备份策略",policy_type="backup",} 0.0

# HELP policy_execution_status Policy execution status (0=pending, 1=running, 2=completed, 3=failed)
# TYPE policy_execution_status gauge
policy_execution_status{alert_instance_name="backup_数据备份策略",instance_id="policy_001",policy_name="数据备份策略",policy_type="backup",} 2.0
```

### 2. 更新资源台账 - `/update_taizhang`

**接口说明**：从OMP系统同步资源台账信息到本地

**请求方式**：POST

**请求示例**：
```bash
curl -X POST "http://localhost:8080/update_taizhang" \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "https://omp.example.com",
    "path": "/api/v0.1/ci_relations/path/s",
    "api_key": "your_api_key",
    "api_secret": "encrypted_api_secret"
  }'
```

**请求参数**：
- `domain` (必填): OMP系统域名
- `path` (必填): API路径
- `api_key` (必填): API密钥
- `api_secret` (必填): 加密的API密钥

**返回结果**：
- 成功: `"The information has been successfully updated locally."`
- 失败: `"The obtained information is abnormal."`

### 3. 获取版本信息 - `/version`

**接口说明**：获取应用版本信息

**请求方式**：GET

**请求示例**：
```bash
curl -X GET "http://localhost:8080/version"
```

**返回格式**：
```
Version 0.0.1, Version time 2025/06/12.
```

### 4. 健康检查 - `/health`

**接口说明**：检查应用健康状态

**请求方式**：GET

**请求示例**：
```bash
curl -X GET "http://localhost:8080/health"
```

**返回结果**：
- 健康: `"OK"`
- 异常: `"FAILED"`

### 5. 刷新缓存 - `/refresh_cache`

**接口说明**：手动刷新配置数据缓存

**请求方式**：POST

**请求示例**：
```bash
curl -X POST "http://localhost:8080/refresh_cache"
```

## 监控指标说明

### 指标类型

1. **cluster_name**: 集群信息指标
   - 标签：包含完整的资源台账信息
   - 值：固定为1.0

2. **policy_execution_result**: 策略执行结果
   - 0: 成功
   - 1: 失败
   - 2: 部分成功

3. **policy_execution_status**: 策略执行状态
   - 0: 待执行 (PENDING)
   - 1: 运行中 (RUNNING)
   - 2: 已完成 (COMPLETED)
   - 3: 失败 (FAILED)

4. **policy_execution_duration_seconds**: 策略执行耗时（秒）

5. **policy_priority**: 策略优先级

### 标签说明

**固定标签**（兼容demo格式）：
- `unit_name`: 单位名称
- `system_name`: 系统名称
- `unit_contact`: 单位联系人
- `unit_contact_phone`: 单位联系人电话
- `unit_contact_mail`: 单位联系人邮箱
- `technical_contact`: 技术联系人
- `technical_contact_phone`: 技术联系人电话
- `technical_contact_mail`: 技术联系人邮箱
- `status`: 状态
- `job_number`: 工单号
- `resource_create_time`: 资源创建时间
- `resource_change_time`: 资源变更时间
- `alert_instance_name`: 告警实例名称
- `instance_id`: 实例ID

**策略特有标签**：
- `policy_name`: 策略名称
- `policy_type`: 策略类型

## 配置文件说明

### policy_data.json
策略执行数据配置文件，包含策略的执行信息：
```json
[
  {
    "policy_name": "数据备份策略",
    "policy_id": "policy_001",
    "policy_type": "backup",
    "execution_result": 0,
    "execution_status": "COMPLETED",
    "execution_duration": 30000,
    "priority": 1,
    "last_execution_time": [2025, 6, 12, 17, 40, 30, 28603000],
    "next_execution_time": [2025, 6, 13, 17, 40, 30, 28603000]
  }
]
```

### taizhang.txt
资源台账数据文件，兼容demo格式：
```json
{
  "result": [
    {
      "instance_id": "policy_001",
      "units_name": "信息技术部",
      "system_name": "策略管理系统",
      "DanWeiLianXiRen": "张三",
      "JiShuLianXiRen": "李四"
    }
  ]
}
```

## 启动和部署

### 本地开发
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 应用将在 http://localhost:8080 启动
```

### 生产部署
```bash
# 打包应用
mvn clean package

# 运行JAR文件
java -jar target/dataInterface-0.0.1-SNAPSHOT.jar
```

## 注意事项

1. **端口配置**：默认使用8080端口，可通过application.properties修改
2. **数据文件**：首次启动会自动生成示例配置文件
3. **编码问题**：中文字符在某些终端可能显示异常，但不影响API功能
4. **缓存机制**：配置数据有1分钟缓存，可通过/refresh_cache手动刷新
5. **兼容性**：完全兼容Python demo的API格式和数据结构
