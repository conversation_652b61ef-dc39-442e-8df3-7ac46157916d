package com.tongdao.datainterface.exception;

import com.tongdao.datainterface.model.enums.ErrorCode;

/**
 * 自定义业务异常类
 * 用于处理业务逻辑中的异常情况
 */
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    /**
     * 错误详情
     */
    private final Object details;
    
    /**
     * 构造函数 - 使用ErrorCode枚举
     * 
     * @param errorCode 错误码枚举
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.details = null;
    }
    
    /**
     * 构造函数 - 使用ErrorCode枚举和自定义消息
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误消息
     */
    public BusinessException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.code = errorCode.getCode();
        this.message = customMessage;
        this.details = null;
    }
    
    /**
     * 构造函数 - 使用ErrorCode枚举、自定义消息和详情
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误消息
     * @param details 错误详情
     */
    public BusinessException(ErrorCode errorCode, String customMessage, Object details) {
        super(customMessage);
        this.code = errorCode.getCode();
        this.message = customMessage;
        this.details = details;
    }
    
    /**
     * 构造函数 - 使用ErrorCode枚举和原因异常
     * 
     * @param errorCode 错误码枚举
     * @param cause 原因异常
     */
    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
        this.details = null;
    }
    
    /**
     * 构造函数 - 使用自定义错误码和消息
     * 
     * @param code 错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.details = null;
    }
    
    /**
     * 构造函数 - 使用自定义错误码、消息和详情
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param details 错误详情
     */
    public BusinessException(Integer code, String message, Object details) {
        super(message);
        this.code = code;
        this.message = message;
        this.details = details;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public Integer getCode() {
        return code;
    }
    
    /**
     * 获取错误消息
     * 
     * @return 错误消息
     */
    @Override
    public String getMessage() {
        return message;
    }
    
    /**
     * 获取错误详情
     * 
     * @return 错误详情
     */
    public Object getDetails() {
        return details;
    }
    
    /**
     * 创建参数验证异常
     * 
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException badRequest(String message) {
        return new BusinessException(ErrorCode.BAD_REQUEST, message);
    }
    
    /**
     * 创建数据不存在异常
     * 
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(ErrorCode.DATA_NOT_FOUND, message);
    }
    
    /**
     * 创建解密失败异常
     * 
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException decryptFailed(String message) {
        return new BusinessException(ErrorCode.DECRYPT_FAILED, message);
    }
    
    /**
     * 创建外部服务调用异常
     * 
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException externalServiceError(String message) {
        return new BusinessException(ErrorCode.EXTERNAL_SERVICE_ERROR, message);
    }
    
    /**
     * 创建监控指标收集异常
     * 
     * @param message 错误消息
     * @return BusinessException实例
     */
    public static BusinessException metricsCollectionFailed(String message) {
        return new BusinessException(ErrorCode.METRICS_COLLECTION_FAILED, message);
    }
}
