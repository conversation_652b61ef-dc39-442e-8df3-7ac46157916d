#!/bin/bash
# 分离式JAR启动脚本
# 支持多种启动模式

JAVA_OPTS="-Xms512m -Xmx1024m"
JAR_FILE="target/dataInterface-0.0.1-SNAPSHOT-thin.jar"
LIB_DIR="target/lib"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "Error: JAR file not found: $JAR_FILE"
    echo "Please run: mvn package -DskipTests -P thin-jar"
    exit 1
fi

# 检查lib目录是否存在
if [ ! -d "$LIB_DIR" ]; then
    echo "Error: Lib directory not found: $LIB_DIR"
    echo "Please run: mvn package -DskipTests -P thin-jar"
    exit 1
fi

echo "========================================"
echo "  DataInterface Thin JAR Launcher"
echo "========================================"
echo "JAR File: $JAR_FILE (应用代码)"
echo "Lib Dir:  $LIB_DIR (外部依赖)"
echo "Java Opts: $JAVA_OPTS"
echo "========================================"

# 显示JAR大小
JAR_SIZE=$(stat -f%z "$JAR_FILE" 2>/dev/null || stat -c%s "$JAR_FILE" 2>/dev/null)
JAR_SIZE_KB=$((JAR_SIZE / 1024))
echo "Application JAR: ${JAR_SIZE_KB} KB"

echo "Starting application..."
java $JAVA_OPTS -cp "$JAR_FILE:$LIB_DIR/*" com.tongdao.datainterface.DataInterfaceApplication
