package com.tongdao.datainterface.config;

import com.tongdao.datainterface.util.HttpUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

/**
 * 应用配置类
 * 配置应用级别的Bean和属性
 */
@Configuration
@EnableConfigurationProperties(AppConfig.AppProperties.class)
public class AppConfig {

    /**
     * 配置HTTP客户端连接池
     */
    @Bean
    public CloseableHttpClient httpClient() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);
        
        return HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build();
    }

    /**
     * 配置RestTemplate
     */
    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setHttpClient(httpClient());
        factory.setConnectTimeout(5000);
        factory.setConnectionRequestTimeout(5000);

        RestTemplate restTemplate = new RestTemplate(factory);

        // 设置到HttpUtil中供静态方法使用
        HttpUtil.setRestTemplate(restTemplate);

        return restTemplate;
    }

    /**
     * 配置应用属性Bean
     */
    @Bean
    public AppProperties appProperties() {
        return new AppProperties();
    }

    /**
     * 应用属性配置
     */
    @ConfigurationProperties(prefix = "app")
    public static class AppProperties {
        private String version;
        private String name;
        private String description;

        // Getters and Setters
        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
