# DataInterface 分离式打包说明

## 🚀 快速开始（默认分离式打包）

### 构建应用
```bash
# 默认构建（分离式JAR）
mvn clean package -DskipTests
```

**构建结果：**
- `target/dataInterface-0.0.1-SNAPSHOT.jar` - 应用代码（130KB）
- `target/lib/` - 依赖库（35MB，51个JAR文件）

### 启动应用
```bash
# 方式1：直接启动
java -cp "target/dataInterface-0.0.1-SNAPSHOT.jar;target/lib/*" com.tongdao.datainterface.DataInterfaceApplication

# 方式2：使用启动脚本
scripts/start-thin.bat  # Windows
scripts/start-thin.sh   # Linux/Mac
```

## 📦 其他打包模式

### Fat JAR（单文件部署）
```bash
mvn clean package -DskipTests -P fat-jar
java -jar target/dataInterface-0.0.1-SNAPSHOT.jar
```

### 超级瘦JAR（依赖分类）
```bash
mvn clean package -DskipTests -P ultra-thin
# 依赖分类存储在：
# - target/lib/spring/ (Spring相关)
# - target/lib/third-party/ (第三方库)
# - target/lib/ (统一目录，兼容性)
```

## ⚡ 快速更新优势

**传统Fat JAR更新：**
- 每次更新需传输：35MB
- 网络传输时间：较长

**分离式JAR更新：**
- 每次更新仅需传输：130KB
- 网络传输时间：极短
- 传输效率提升：**270倍**

## 🛠️ 部署建议

- **开发环境**：使用默认分离式打包
- **测试环境**：使用分离式打包
- **生产环境**：根据部署方式选择
  - 传统服务器：分离式JAR
  - 容器化：Fat JAR或分层JAR
  - 单文件部署：Fat JAR

详细说明请参考：[PACKAGING.md](PACKAGING.md)
