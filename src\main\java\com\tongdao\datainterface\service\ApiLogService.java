package com.tongdao.datainterface.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tongdao.datainterface.model.entity.ApiLogEntry;
import com.tongdao.datainterface.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * API日志记录服务
 * 负责记录API请求和响应的详细信息，并进行敏感信息脱敏处理
 */
@Service
public class ApiLogService {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiLogService.class);
    
    /**
     * API日志专用Logger，用于输出结构化日志
     */
    private static final Logger apiLogger = LoggerFactory.getLogger("API_LOG");
    
    private final ObjectMapper objectMapper;
    
    /**
     * 敏感信息字段名称模式（不区分大小写）
     */
    private static final Set<String> SENSITIVE_FIELD_NAMES = Set.of(
            "password", "passwd", "pwd", "secret", "token", "key", "authorization",
            "api_secret", "apiSecret", "api_key", "apiKey", "access_token", "accessToken",
            "refresh_token", "refreshToken", "session_id", "sessionId", "cookie"
    );
    
    /**
     * 敏感信息值的正则表达式模式
     */
    private static final List<Pattern> SENSITIVE_VALUE_PATTERNS = Arrays.asList(
            Pattern.compile("(?i)bearer\\s+[a-zA-Z0-9\\-._~+/]+=*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("(?i)basic\\s+[a-zA-Z0-9+/]+=*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("[a-fA-F0-9]{32,}", Pattern.CASE_INSENSITIVE), // 32位以上的十六进制字符串
            Pattern.compile("[a-zA-Z0-9\\-._~+/]{40,}", Pattern.CASE_INSENSITIVE) // 40位以上的Base64类似字符串
    );
    
    public ApiLogService() {
        this.objectMapper = JsonUtil.getObjectMapper();
        // 配置美化输出
        this.objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.INDENT_OUTPUT, true);
    }
    
    /**
     * 记录API请求开始（简化版，不包含请求体）
     *
     * @param requestId 请求ID
     * @param request HTTP请求对象
     * @param requestBody 请求体内容（可能为null）
     */
    public void logRequestStart(String requestId, HttpServletRequest request, String requestBody) {
        try {
            // 创建简化的请求信息，用于请求开始日志
            String summary = String.format("REQUEST_START [%s] %s %s from %s",
                    requestId,
                    request.getMethod(),
                    request.getRequestURI(),
                    getClientIpAddress(request));

            // 如果有查询参数，也显示出来
            if (request.getQueryString() != null) {
                summary += "?" + request.getQueryString();
            }

            apiLogger.info(summary);

        } catch (Exception e) {
            logger.error("Failed to log request start for requestId: {}", requestId, e);
        }
    }
    
    /**
     * 记录API请求完成
     * 
     * @param requestId 请求ID
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param requestBody 请求体内容
     * @param responseBody 响应体内容
     * @param startTime 请求开始时间
     * @param exception 异常信息（如果有）
     */
    public void logRequestComplete(String requestId, HttpServletRequest request, 
                                 HttpServletResponse response, String requestBody, 
                                 String responseBody, LocalDateTime startTime, 
                                 Exception exception) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            long processingTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            ApiLogEntry.RequestInfo requestInfo = buildRequestInfo(request, requestBody);
            ApiLogEntry.ResponseInfo responseInfo = buildResponseInfo(response, responseBody);
            
            ApiLogEntry logEntry = ApiLogEntry.builder()
                    .requestId(requestId)
                    .requestTime(startTime)
                    .responseTime(endTime)
                    .processingTimeMs(processingTime)
                    .clientIp(getClientIpAddress(request))
                    .userAgent(request.getHeader("User-Agent"))
                    .requestInfo(requestInfo)
                    .responseInfo(responseInfo)
                    .exceptionMessage(exception != null ? exception.getMessage() : null)
                    .build();
            
            String logJson = formatJsonForReadability(logEntry);

            if (exception != null) {
                apiLogger.error("REQUEST_ERROR:\n{}", logJson);
            } else {
                apiLogger.info("REQUEST_COMPLETE:\n{}", logJson);
            }
            
        } catch (Exception e) {
            logger.error("Failed to log request complete for requestId: {}", requestId, e);
        }
    }
    
    /**
     * 构建请求信息
     */
    private ApiLogEntry.RequestInfo buildRequestInfo(HttpServletRequest request, String requestBody) {
        return ApiLogEntry.RequestInfo.builder()
                .method(request.getMethod())
                .uri(request.getRequestURI())
                .queryParams(extractQueryParams(request))
                .headers(extractAndSanitizeHeaders(request))
                .body(sanitizeContent(requestBody))
                .contentType(request.getContentType())
                .contentLength(request.getContentLengthLong() >= 0 ? request.getContentLengthLong() : null)
                .build();
    }
    
    /**
     * 构建响应信息
     */
    private ApiLogEntry.ResponseInfo buildResponseInfo(HttpServletResponse response, String responseBody) {
        return ApiLogEntry.ResponseInfo.builder()
                .status(response.getStatus())
                .statusText(getStatusText(response.getStatus()))
                .headers(extractResponseHeaders(response))
                .body(sanitizeContent(responseBody))
                .contentType(response.getContentType())
                .contentLength(responseBody != null ? (long) responseBody.length() : null)
                .build();
    }
    
    /**
     * 提取查询参数
     */
    private Map<String, String> extractQueryParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        if (request.getQueryString() != null) {
            request.getParameterMap().forEach((key, values) -> {
                if (values != null && values.length > 0) {
                    String value = values[0];
                    params.put(key, isSensitiveField(key) ? maskSensitiveValue(value) : value);
                }
            });
        }
        return params.isEmpty() ? null : params;
    }
    
    /**
     * 提取并脱敏请求头
     */
    private Map<String, String> extractAndSanitizeHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Collections.list(request.getHeaderNames()).forEach(headerName -> {
            String headerValue = request.getHeader(headerName);
            if (headerValue != null) {
                headers.put(headerName, isSensitiveField(headerName) ? 
                           maskSensitiveValue(headerValue) : headerValue);
            }
        });
        return headers.isEmpty() ? null : headers;
    }
    
    /**
     * 提取响应头
     */
    private Map<String, String> extractResponseHeaders(HttpServletResponse response) {
        Map<String, String> headers = new HashMap<>();
        response.getHeaderNames().forEach(headerName -> {
            String headerValue = response.getHeader(headerName);
            if (headerValue != null) {
                headers.put(headerName, headerValue);
            }
        });
        return headers.isEmpty() ? null : headers;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 取第一个IP地址
                return ip.split(",")[0].trim();
            }
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 获取HTTP状态码描述
     */
    private String getStatusText(int status) {
        return switch (status) {
            case 200 -> "OK";
            case 201 -> "Created";
            case 400 -> "Bad Request";
            case 401 -> "Unauthorized";
            case 403 -> "Forbidden";
            case 404 -> "Not Found";
            case 500 -> "Internal Server Error";
            default -> "HTTP " + status;
        };
    }
    
    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        if (fieldName == null) return false;
        return SENSITIVE_FIELD_NAMES.contains(fieldName.toLowerCase());
    }
    
    /**
     * 脱敏敏感内容
     */
    private String sanitizeContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }
        
        // 限制内容长度，避免日志过大
        if (content.length() > 10000) {
            content = content.substring(0, 10000) + "...[TRUNCATED]";
        }
        
        // 脱敏敏感值
        String sanitized = content;
        for (Pattern pattern : SENSITIVE_VALUE_PATTERNS) {
            sanitized = pattern.matcher(sanitized).replaceAll("[MASKED]");
        }
        
        // 脱敏JSON中的敏感字段
        sanitized = sanitizeJsonContent(sanitized);
        
        return sanitized;
    }
    
    /**
     * 脱敏JSON内容中的敏感字段
     */
    private String sanitizeJsonContent(String jsonContent) {
        try {
            // 尝试解析为JSON并脱敏
            if (jsonContent.trim().startsWith("{") || jsonContent.trim().startsWith("[")) {
                Object jsonObj = objectMapper.readValue(jsonContent, Object.class);
                Object sanitizedObj = sanitizeJsonObject(jsonObj);
                return objectMapper.writeValueAsString(sanitizedObj);
            }
        } catch (JsonProcessingException e) {
            // 如果不是有效的JSON，直接返回原内容
        }
        return jsonContent;
    }
    
    /**
     * 递归脱敏JSON对象
     */
    @SuppressWarnings("unchecked")
    private Object sanitizeJsonObject(Object obj) {
        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            Map<String, Object> sanitizedMap = new HashMap<>();
            map.forEach((key, value) -> {
                if (isSensitiveField(key)) {
                    sanitizedMap.put(key, maskSensitiveValue(String.valueOf(value)));
                } else {
                    sanitizedMap.put(key, sanitizeJsonObject(value));
                }
            });
            return sanitizedMap;
        } else if (obj instanceof List) {
            List<Object> list = (List<Object>) obj;
            return list.stream().map(this::sanitizeJsonObject).toList();
        }
        return obj;
    }
    
    /**
     * 掩码敏感值
     */
    private String maskSensitiveValue(String value) {
        if (value == null || value.length() <= 4) {
            return "[MASKED]";
        }

        // 保留前2位和后2位，中间用*替代
        int len = value.length();
        if (len <= 8) {
            return value.substring(0, 2) + "***" + value.substring(len - 2);
        } else {
            return value.substring(0, 3) + "***" + value.substring(len - 3);
        }
    }

    /**
     * 格式化JSON为可读性更好的格式
     */
    private String formatJsonForReadability(ApiLogEntry logEntry) {
        try {
            StringBuilder sb = new StringBuilder();

            // 添加简洁的头部信息
            sb.append("=== API LOG ").append("=".repeat(60)).append("\n");
            sb.append("Request ID: ").append(logEntry.getRequestId()).append("\n");

            if (logEntry.getRequestInfo() != null) {
                sb.append("Method: ").append(logEntry.getRequestInfo().getMethod());
                sb.append(" | URI: ").append(logEntry.getRequestInfo().getUri()).append("\n");
            }

            if (logEntry.getClientIp() != null) {
                sb.append("Client IP: ").append(logEntry.getClientIp());
                if (logEntry.getProcessingTimeMs() != null) {
                    sb.append(" | Processing Time: ").append(logEntry.getProcessingTimeMs()).append("ms");
                }
                sb.append("\n");
            }

            sb.append("-".repeat(80)).append("\n");

            // 添加格式化的JSON
            String prettyJson = objectMapper.writeValueAsString(logEntry);
            sb.append(prettyJson);

            sb.append("\n").append("=".repeat(80));

            return sb.toString();
        } catch (JsonProcessingException e) {
            logger.error("Failed to format JSON for readability", e);
            // 如果格式化失败，返回简单的字符串表示
            return "=== API LOG ERROR ===\nFailed to format: " + logEntry.toString() + "\n" + "=".repeat(20);
        }
    }
}
