package com.tongdao.datainterface.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 系统版本信息DTO
 * 用于返回系统版本和基本信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemVersionDto {
    
    /**
     * 应用名称
     */
    @JsonProperty("app_name")
    private String appName;
    
    /**
     * 应用版本
     */
    @JsonProperty("version")
    private String version;
    
    /**
     * 应用描述
     */
    @JsonProperty("description")
    private String description;
    
   /* *//**
     * 构建时间
     *//*
    @JsonProperty("build_time")
    private String buildTime;
    
    *//**
     * Java版本
     *//*
    @JsonProperty("java_version")
    private String javaVersion;
    
    *//**
     * Spring Boot版本
     *//*
    @JsonProperty("spring_boot_version")
    private String springBootVersion;
    
    *//**
     * 系统启动时间
     *//*
    @JsonProperty("startup_time")
    private LocalDateTime startupTime;
    
    *//**
     * 当前时间
     *//*
    @JsonProperty("current_time")
    private LocalDateTime currentTime;
    
    *//**
     * 运行时长（秒）
     *//*
    @JsonProperty("uptime_seconds")
    private Long uptimeSeconds;*/
}
