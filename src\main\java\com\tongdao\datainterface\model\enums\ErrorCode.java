package com.tongdao.datainterface.model.enums;

/**
 * 错误码枚举
 * 定义系统中使用的错误码和错误消息
 */
public enum ErrorCode {
    
    // ========== 通用错误码 ==========
    SUCCESS(200, "操作成功"),
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    
    // ========== 业务错误码 ==========
    INVALID_PRODUCT_TYPE(1001, "无效的产品类型"),
    INVALID_CREDENTIALS(1002, "无效的认证信息"),
    DECRYPT_FAILED(1003, "解密失败"),
    ENCRYPT_FAILED(1004, "加密失败"),
    
    // ========== 数据相关错误码 ==========
    DATA_NOT_FOUND(2001, "数据不存在"),
    DATA_PARSE_ERROR(2002, "数据解析错误"),
    DATA_VALIDATION_ERROR(2003, "数据验证失败"),
    RESOURCE_SYNC_FAILED(2004, "资源台账同步失败"),
    
    // ========== 外部服务错误码 ==========
    EXTERNAL_SERVICE_ERROR(3001, "外部服务调用失败"),
    EXTERNAL_API_ERROR(3002, "外部API调用失败"),
    OMP_API_ERROR(3003, "OMP API调用失败"),
    DATABASE_CONNECTION_ERROR(3004, "数据库连接失败"),
    NETWORK_ERROR(3005, "网络连接错误"),

    // ========== 系统内部错误码 ==========
    INTERNAL_ERROR(5001, "系统内部错误"),
    CONFIG_ERROR(5002, "配置错误"),
    FILE_IO_ERROR(5003, "文件读写错误"),
    
    // ========== 监控相关错误码 ==========
    METRICS_COLLECTION_FAILED(4001, "监控指标收集失败"),
    CLUSTER_CONNECTION_FAILED(4002, "集群连接失败"),
    INVALID_METRICS_FORMAT(4003, "无效的监控指标格式"),
    METRICS_DATA_INCOMPLETE(4004, "监控指标数据不完整");

    private final Integer code;
    private final String message;

    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码获取枚举
     */
    public static ErrorCode fromCode(Integer code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
