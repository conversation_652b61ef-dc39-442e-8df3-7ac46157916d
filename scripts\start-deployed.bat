@echo off
REM 部署后的启动脚本
REM 从deploy目录启动应用

set JAVA_OPTS=-Xms512m -Xmx1024m
set DEPLOY_DIR=deploy

cd /d "%~dp0..\%DEPLOY_DIR%"

echo ========================================
echo   DataInterface 生产环境启动
echo ========================================
echo 工作目录: %CD%
echo Java参数: %JAVA_OPTS%
echo ========================================

java %JAVA_OPTS% -cp "dataInterface-0.0.1-SNAPSHOT.jar;lib\*" com.tongdao.datainterface.DataInterfaceApplication
