package com.tongdao.datainterface.model.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 监控指标数据实体类
 * 基于demo文件中的监控指标结构设计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MetricData {
    
    /**
     * 告警实例名称
     */
    @JsonProperty("alert_instance_name")
    private String alertInstanceName;
    
    /**
     * 实例ID
     */
    @JsonProperty("instance_id")
    private String instanceId;
    
    /**
     * 集群名称
     */
    @JsonProperty("cluster_name")
    private String clusterName;
    
    /**
     * 数据库名称
     */
    @JsonProperty("db_name")
    private String dbName;
    
    /**
     * 产品版本
     */
    @JsonProperty("version")
    private String version;
    
    /**
     * 运行状态
     */
    @JsonProperty("running_status")
    private String runningStatus;
    
    /**
     * 数据收集时间
     */
    @JsonProperty("collect_time")
    private LocalDateTime collectTime;
    
    // ========== DWS监控指标 ==========
    
    /**
     * 共享缓冲区命中率
     */
    @JsonProperty("dws001_shared_buffer_hit_ratio")
    private Double sharedBufferHitRatio;
    
    /**
     * 内存排序比率
     */
    @JsonProperty("dws002_in_memory_sort_ratio")
    private Double inMemorySortRatio;
    
    /**
     * 物理读取次数
     */
    @JsonProperty("dws003_physical_reads")
    private Double physicalReads;
    
    /**
     * 物理写入次数
     */
    @JsonProperty("dws004_physical_writes")
    private Double physicalWrites;
    
    /**
     * 每秒物理读取次数
     */
    @JsonProperty("dws005_physical_reads_per_second")
    private Double physicalReadsPerSecond;
    
    /**
     * 每秒物理写入次数
     */
    @JsonProperty("dws006_physical_writes_per_second")
    private Double physicalWritesPerSecond;
    
    /**
     * 数据库大小
     */
    @JsonProperty("dws007_db_size")
    private Double dbSize;
    
    /**
     * 活跃SQL数量
     */
    @JsonProperty("dws008_active_sql_count")
    private Double activeSqlCount;
    
    /**
     * 会话数量
     */
    @JsonProperty("dws009_session_count")
    private Double sessionCount;
    
    // ========== TDATA监控指标 ==========
    
    /**
     * TDATA状态
     */
    @JsonProperty("tdata_status")
    private String tdataStatus;
    
    /**
     * CPU配额
     */
    @JsonProperty("tdata_cpu_quota")
    private Double cpuQuota;
    
    /**
     * CPU使用率
     */
    @JsonProperty("tdata_cpu_ratio")
    private Double cpuRatio;
    
    /**
     * 内存配额
     */
    @JsonProperty("tdata_memory_quota")
    private Double memoryQuota;
    
    /**
     * 内存使用率
     */
    @JsonProperty("tdata_memory_ratio")
    private Double memoryRatio;
    
    /**
     * 存储配额
     */
    @JsonProperty("tdata_storage_quota")
    private Double storageQuota;
    
    /**
     * 存储使用率
     */
    @JsonProperty("tdata_storage_ratio")
    private Double storageRatio;
    
    /**
     * 离线磁盘数
     */
    @JsonProperty("tdata_offline_disk")
    private Integer offlineDisk;
    
    /**
     * 扩展指标数据
     * 用于存储其他动态指标
     */
    @JsonProperty("extended_metrics")
    private Map<String, Object> extendedMetrics;
}
