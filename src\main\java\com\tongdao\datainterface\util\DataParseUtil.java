package com.tongdao.datainterface.util;

import com.tongdao.datainterface.model.entity.MetricData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 数据解析工具类
 * 处理不同格式的监控数据解析
 */
public class DataParseUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DataParseUtil.class);
    
    // TDATA数据格式的分隔符模式
    private static final Pattern COMMA_PATTERN = Pattern.compile(",");
    private static final Pattern COLON_PATTERN = Pattern.compile(":");
    
    /**
     * 解析TDATA格式的监控数据
     * 格式：clusterName:tdata1,dbName:oral,DBID:1304675084,runningStatus:OPEN,version:11.2.0.4,cpu:10,cpuUsage:13.96,memory:40,memoryUsage:78.46,storage:994,storageUsage:14.19
     * 
     * @param dataLine 数据行
     * @return 解析后的数据Map
     */
    public static Map<String, String> parseTdataLine(String dataLine) {
        Map<String, String> data = new HashMap<>();
        
        if (dataLine == null || dataLine.trim().isEmpty()) {
            return data;
        }
        
        try {
            // 按逗号分割
            String[] items = COMMA_PATTERN.split(dataLine.trim());
            
            for (String item : items) {
                if (item.contains(":")) {
                    String[] keyValue = COLON_PATTERN.split(item, 2);
                    if (keyValue.length == 2) {
                        data.put(keyValue[0].trim(), keyValue[1].trim());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse TDATA line: {}", dataLine, e);
        }
        
        return data;
    }
    
    /**
     * 解析TDATA文件内容
     * 
     * @param fileContent 文件内容
     * @return 解析后的数据列表
     */
    public static List<Map<String, String>> parseTdataFile(String fileContent) {
        List<Map<String, String>> result = new ArrayList<>();
        
        if (fileContent == null || fileContent.trim().isEmpty()) {
            return result;
        }
        
        String[] lines = fileContent.split("\n");
        for (String line : lines) {
            if (line.trim().isEmpty()) {
                continue;
            }
            
            Map<String, String> data = parseTdataLine(line);
            if (!data.isEmpty()) {
                result.add(data);
            }
        }
        
        return result;
    }
    
    /**
     * 将TDATA原始数据转换为MetricData对象
     * 
     * @param rawData 原始数据Map
     * @return MetricData对象
     */
    public static MetricData convertTdataToMetricData(Map<String, String> rawData) {
        if (rawData == null || rawData.isEmpty()) {
            return null;
        }
        
        try {
            return MetricData.builder()
                    .clusterName(rawData.get("clusterName"))
                    .dbName(rawData.get("dbName"))
                    .instanceId(rawData.get("DBID"))
                    .alertInstanceName(rawData.get("clusterName") + "_" + rawData.get("dbName"))
                    .version(rawData.get("version"))
                    .runningStatus(rawData.get("runningStatus"))
                    .tdataStatus(rawData.get("runningStatus"))
                    .cpuQuota(parseDouble(rawData.get("cpu")))
                    .cpuRatio(parseDouble(rawData.get("cpuUsage")))
                    .memoryQuota(parseDouble(rawData.get("memory")))
                    .memoryRatio(parseDouble(rawData.get("memoryUsage")))
                    .storageQuota(parseDouble(rawData.get("storage")))
                    .storageRatio(parseDouble(rawData.get("storageUsage")))
                    .offlineDisk(parseInt(rawData.get("offline_disk")))
                    .build();
        } catch (Exception e) {
            logger.error("Failed to convert TDATA raw data to MetricData: {}", rawData, e);
            return null;
        }
    }
    
    /**
     * 解析DWS API响应数据
     * 
     * @param apiResponse DWS API响应JSON字符串
     * @return 解析后的指标数据列表
     */
    public static List<MetricData> parseDwsApiResponse(String apiResponse) {
        List<MetricData> result = new ArrayList<>();
        
        try {
            Map<String, Object> responseMap = JsonUtil.fromJsonStringToMap(apiResponse);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseMap.get("datas");
            
            if (dataList != null) {
                for (Map<String, Object> data : dataList) {
                    MetricData metricData = convertDwsDataToMetricData(data);
                    if (metricData != null) {
                        result.add(metricData);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse DWS API response: {}", apiResponse, e);
        }
        
        return result;
    }
    
    /**
     * 将DWS原始数据转换为MetricData对象
     * 
     * @param rawData 原始数据Map
     * @return MetricData对象
     */
    public static MetricData convertDwsDataToMetricData(Map<String, Object> rawData) {
        if (rawData == null || rawData.isEmpty()) {
            return null;
        }
        
        try {
            String instanceId = (String) rawData.get("datastore_id");
            
            return MetricData.builder()
                    .instanceId(instanceId)
                    .alertInstanceName(instanceId)
                    .sharedBufferHitRatio(parseDouble(rawData.get("dws001_shared_buffer_hit_ratio")))
                    .inMemorySortRatio(parseDouble(rawData.get("dws002_in_memory_sort_ratio")))
                    .physicalReads(parseDouble(rawData.get("dws003_physical_reads")))
                    .physicalWrites(parseDouble(rawData.get("dws004_physical_writes")))
                    .physicalReadsPerSecond(parseDouble(rawData.get("dws005_physical_reads_per_second")))
                    .physicalWritesPerSecond(parseDouble(rawData.get("dws006_physical_writes_per_second")))
                    .dbSize(parseDouble(rawData.get("dws007_db_size")))
                    .activeSqlCount(parseDouble(rawData.get("dws008_active_sql_count")))
                    .sessionCount(parseDouble(rawData.get("dws009_session_count")))
                    .build();
        } catch (Exception e) {
            logger.error("Failed to convert DWS raw data to MetricData: {}", rawData, e);
            return null;
        }
    }
    
    /**
     * 构建OMP API密钥参数
     * 兼容Python demo中的build_api_key函数
     * 
     * @param path API路径
     * @param params 请求参数
     * @param apiKey API密钥
     * @param apiSecret API密钥
     * @return 构建后的参数Map
     */
    public static Map<String, Object> buildApiKey(String path, Map<String, Object> params, String apiKey, String apiSecret) {
        if (params == null) {
            params = new HashMap<>();
        }
        
        try {
            // 获取所有参数值并排序
            List<String> values = new ArrayList<>();
            params.entrySet().stream()
                    .filter(entry -> !entry.getKey().equals("_key") && !entry.getKey().equals("_secret"))
                    .filter(entry -> !(entry.getValue() instanceof Map) && !(entry.getValue() instanceof List))
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> values.add(String.valueOf(entry.getValue())));
            
            // 构建签名字符串
            String valueString = String.join("", values);
            String signString = path + apiSecret + valueString;
            
            // 计算SHA1哈希
            String signature = HashUtil.sha1(signString);
            
            // 添加签名参数
            params.put("_secret", signature);
            params.put("_key", apiKey);
            
        } catch (Exception e) {
            logger.error("Failed to build API key: path={}, apiKey={}", path, apiKey, e);
            throw new RuntimeException("构建API密钥失败", e);
        }
        
        return params;
    }
    
    /**
     * 安全解析Double值
     * 
     * @param value 字符串值
     * @return Double值，解析失败返回null
     */
    private static Double parseDouble(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            logger.debug("Failed to parse double value: {}", value);
            return null;
        }
    }
    
    /**
     * 安全解析Integer值
     * 
     * @param value 字符串值
     * @return Integer值，解析失败返回null
     */
    private static Integer parseInt(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            logger.debug("Failed to parse integer value: {}", value);
            return null;
        }
    }
}
