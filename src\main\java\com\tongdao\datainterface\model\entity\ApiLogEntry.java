package com.tongdao.datainterface.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * API日志记录实体
 * 用于记录API请求和响应的详细信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiLogEntry {
    
    /**
     * 请求唯一标识符
     */
    private String requestId;
    
    /**
     * 请求时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime requestTime;
    
    /**
     * 响应时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime responseTime;
    
    /**
     * 请求处理耗时（毫秒）
     */
    private Long processingTimeMs;
    
    /**
     * 请求信息
     */
    private RequestInfo requestInfo;
    
    /**
     * 响应信息
     */
    private ResponseInfo responseInfo;
    
    /**
     * 异常信息（如果有）
     */
    private String exceptionMessage;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 请求信息内嵌类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RequestInfo {
        
        /**
         * HTTP请求方法
         */
        private String method;
        
        /**
         * 请求URI
         */
        private String uri;
        
        /**
         * 查询参数
         */
        private Map<String, String> queryParams;
        
        /**
         * 请求头信息（已脱敏）
         */
        private Map<String, String> headers;
        
        /**
         * 请求体内容（已脱敏）
         */
        private String body;
        
        /**
         * 内容类型
         */
        private String contentType;
        
        /**
         * 内容长度
         */
        private Long contentLength;
    }
    
    /**
     * 响应信息内嵌类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResponseInfo {
        
        /**
         * HTTP状态码
         */
        private Integer status;
        
        /**
         * 状态描述
         */
        private String statusText;
        
        /**
         * 响应头信息
         */
        private Map<String, String> headers;
        
        /**
         * 响应体内容（已脱敏）
         */
        private String body;
        
        /**
         * 内容类型
         */
        private String contentType;
        
        /**
         * 内容长度
         */
        private Long contentLength;
    }
}
