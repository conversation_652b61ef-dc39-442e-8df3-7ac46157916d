#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
File: tdata_exporter.py
Time: 2023/4/7 14:14:22
Author: lidarong
Version: Python 3.8.20
Desc: None
'''

import os
import sys
import json
import re
import requests
import argparse
import logging
import traceback
import hashlib
import urllib3
import datetime
from flask import Flask, request, Response
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import prometheus_client
from prometheus_client import Gauge
from prometheus_client.core import CollectorRegistry
urllib3.disable_warnings()


# 定义版本号
def get_version():
    return 'Version 0.0.1, Version time 2025/4/3.'


# 定义传入的参数
description = "Welcome To Use TDATA Exporter."
parser = argparse.ArgumentParser(description=description)
parser.add_argument('--host', default='0.0.0.0', help='IPv4 Listen Address.')
parser.add_argument('--port', default='16003', help='Service Listen Port.')
parser.add_argument('--version', action='version', version=get_version(), help='Application Version.')
args = parser.parse_args()

# 固定函数，不需要更改
def decrypt(private_key_str: str, message: str) -> str:
    '''
    RSA 非对称解密函数
    private_key_str: 解密私钥文本
    message: 解密文本内容
    '''
    private_key = RSA.import_key(private_key_str)
    cipher = PKCS1_OAEP.new(private_key)
    plain_text = cipher.decrypt(message).decode()

    return plain_text

# 可扩展函数，根据各个产品特性进行编写
def get_tdata_result(file_path: str) -> list:
    '''
    读取TDATA在OEM-Server上的文件，prometheus_data.txt，格式如下
    clusterName:tdata1  dbName:oral  DBID:1304675084  runningStatus:OPEN  version:********	cpu:10	cpuUsage:13.96	memory:40  memoryUsage:78.46  storage:994  storageUsage:14.19
    '''
    result = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                if line.strip():
                    # 解析每一行数据
                    data = {}
                    # items = line.strip().split('\t')
                    # items = re.split(r'[\s\t]+', line.strip())
                    items = re.split(r',', line.strip())
                    for item in items:
                        if ':' in item:
                            key, value = item.split(':', 1)
                            data[key.strip()] = value.strip()
                    # logger.info(f"data in yuejizuo is {data}")
                    result.append(data)
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
    return result

# 固定函数，不需要更改
def build_api_key(path, params, api_key, api_secret):
    '''
    解析 OMP API 密钥函数
    path: 请求的接口路径
    param: 请求参数
    api_key: Api Key
    api_secret: Api Secret
    '''

    values = "".join([str(params[k]) for k in sorted((params or {}).keys())
                      if k not in ("_key", "_secret") and not isinstance(params[k], (dict, list))])
    _secret = "".join([path, api_secret, values]).encode("utf-8")
    params["_secret"] = hashlib.sha1(_secret).hexdigest()
    params["_key"] = api_key

    return params

# 可扩展函数，根据各个产品特性进行编写
def find_content(resource_text: list, search_text: str) -> dict:
    '''
    通过资源台账匹配厅局单位信息函数
    resource_text: 资源台账文本
    search_text: 匹配文本
    '''

    # 根据实例 ID 与 OMP 信息进行匹配，并返回相关的实例信息
    for text in resource_text:
        if text["YunXuHao"] is not None and text["YunXuHao"] in search_text:
            text_dict = {
                "unit_name": text.get('units_name', '-'),
                "system_name": text.get('system_name', '-'),
                "unit_contact": text.get('DanWeiLianXiRen', '-'),
                "unit_contact_phone": text.get('DanWeiLianXiRen_Phone', '-'),
                "unit_contact_mail": text.get('DanWeiLianXiRen_Mail', '-'),
                "technical_contact": text.get('JiShuLianXiRen', '-'),
                "technical_contact_phone": text.get('JiShuLianXiRen_Phone', '-'),
                "technical_contact_mail": text.get('JiShuLianXiRen_Mail', '-'),
                "status": text.get('ZhuangTai', '-'),
                "job_number": text.get('JiFeiTiaoZhengDanHao', '-'),
                "resource_create_time": text.get('ZiYuanChuangJianRiQi', '-'),
                "resource_change_time": text.get('JiFeiTiaoZhengRiQI', '-'),
                "project_name": text.get('Project', '-'),
                "resource_set": text.get('ZiYuanJi', '-'),
                "resource_name": text.get('JiQunMingCheng', '-'),
                "DBID":text.get('DBID','-'),
                "cpu": text.get('CPUHe', '-'),
                "memory": text.get('NeiCun_GB', '-'),
                "capacity": text.get('CunChuGB', '-'),
                "SCANIP": text.get('SCANIP', '-'),
                "SuoShuQuYu": text.get('SuoShuQuYu','-')
            }
            return text_dict

# 可扩展函数，根据各个产品特性进行编写
def get_cluster_metrics(file_path: str, resource_text: str) -> str:
    '''
    获取集群指标函数
    file_path: prometheus_data.txt文件路径
    resource_text: OMP 资源信息
    '''
    # 产品集群相关指标定义
    metrics_dict = {
        "tdata_status": "runningStatus",
        "tdata_cpu_quota": "cpu",
        "tdata_cpu_ratio": "cpuUsage",
        "tdata_memory_quota": "memory",
        "tdata_memory_ratio": "memoryUsage",
        "tdata_storage_quota": "storage",
        "tdata_storage_ratio": "storageUsage",
        "tdata_offline_disk": "offline_disk"
    }
    
    REGISTRY = CollectorRegistry(auto_describe=False)
    
    # 定义 cluster_name 的集群信息标签
    cluster_name = Gauge('cluster_name', '', [
        "unit_name", "system_name", "unit_contact", "unit_contact_phone", "unit_contact_mail",
        "technical_contact", "technical_contact_phone", "technical_contact_mail", "status",
        "job_number", "resource_create_time", "resource_change_time", "alert_instance_name",
        "instance_id", "project_name", "resource_set", "resource_name", "cpu", "memory",
        "capacity", "SCANIP", "cluster_name", "db_name", "version", "YunXuHao","SuoShuQuYu"
    ], registry=REGISTRY)
    
    metrics = {}
    for metric_key, metric_obj in metrics_dict.items():
        metrics[metric_key] = Gauge(metric_key, '', ["alert_instance_name", "instance_id", "cluster_name", "db_name"], registry=REGISTRY)
    
    # 读取并处理监控数据
    tdata_results = get_tdata_result(file_path)
    
    try:
        for data in tdata_results:
            # logger.info(f"data in tdata_results:{str(data)}")
            instance_id = data.get('DBID','')
            yunxuhao = data.get('yunxuhao','')
            alert_instance_name = f"{data.get('clusterName', '')}_{data.get('dbName', '')}"
            info = find_content(resource_text=resource_text, search_text=yunxuhao)
            # logger.info(f"info text :{str(info)}")
            # info = resource_text
            # 设置集群信息指标
            cluster_name.labels(
                unit_name=info["unit_name"],
                system_name=info["system_name"],
                SuoShuQuYu=info["SuoShuQuYu"],
                unit_contact=info["unit_contact"],
                unit_contact_phone=info["unit_contact_phone"],
                unit_contact_mail=info["unit_contact_mail"],
                technical_contact=info["technical_contact"],
                technical_contact_phone=info["technical_contact_phone"],
                technical_contact_mail=info["technical_contact_mail"],
                status=info["status"],
                job_number=info["job_number"],
                resource_create_time=info["resource_create_time"],
                resource_change_time=info["resource_change_time"],
                alert_instance_name=alert_instance_name,
                instance_id=instance_id,
                project_name=info["project_name"],
                resource_set=info["resource_set"],
                resource_name=info["resource_name"],
                cpu=info["cpu"],
                memory=info["memory"],
                capacity=info["capacity"],
                SCANIP=info["SCANIP"],
                cluster_name=data.get("clusterName", ""),
                db_name=data.get("dbName", ""),
                version=data.get("version", ""),
                YunXuHao=data.get("yunxuhao","")                
            ).set(1)
            
            # 设置性能指标
            for metric_key, metric_obj in metrics_dict.items():
                try:
                    value = float(data.get(metric_obj, 0))
                    metrics[metric_key].labels(
                        alert_instance_name=alert_instance_name,
                        instance_id=instance_id,
                        cluster_name=data.get("clusterName", ""),
                        db_name=data.get("dbName", "")
                    ).set(value)
                except (ValueError, TypeError):
                    continue
                    
    except Exception as e:
        logger.error(f"Error processing metrics: {str(e)}")
        logger.error(traceback.format_exc())
    
    return prometheus_client.generate_latest(REGISTRY).decode('utf-8')


app = Flask(__name__)

# 固定接口，采集器获取监控数据接口
@app.route('/metrics', methods=['GET'])
def response_metrics(): 
    # 加载资源台账信息
    with open("taizhang.txt", "r", encoding = "utf-8") as f:
        resource_text = json.loads(f.read())

    return_text = get_cluster_metrics(file_path="prometheus_data.txt", resource_text=resource_text["result"])
    return Response(return_text, mimetype='text/plain'), 200


# 固定接口，更新资源台账到本地接口
@app.route('/update_taizhang', methods=['POST'])
def update_taizhang():
    domain = request.json.get('domain', 'https://omp.gdzwy.com')
    path = request.json.get('path', '/api/v0.1/ci_relations/path/s')
    api_key = request.json.get('api_key', 'dbd60e13c23f45068520fd31a3509b1b')
    api_secret = request.json.get('api_secret', '05476111fbff3e7bf653041659804af6bc910061ee0aca2f8ba6c9780b38e1b7edacac9201126e99cc8fb4c4cef85073da20f4f4fb56f3efb28b1d13dc5e6b416734aced14e975a73568395c7f5fbdc0e8a6897c96a45ff5518221ef37138b5c56fc49424e7bc81cca39c4a2b98064d2fa6e4b8b3dacdae212403e1ded33ff76')
    url = domain + path

    # 这里的 payload 为获取不同产品OMP信息的定义，111 代表 TDATA 产品
    payload = {
        "page": 1,
        "page_size": 999999,
        "source": {
            "type_id": 100
        },
        "target": {
            "type_ids": [111]
        },
        "path": [[100, 111]]
    }

    payload = build_api_key(path=path, params=payload, api_key=api_key,
                            api_secret=decrypt(private_key, bytes.fromhex(api_secret)))

    response = requests.post(url, json=payload, verify=False).json()

    try:
        return_list = []
        for i in response["paths"]["系统-TDATA"]:
            return_dict = {}
            return_dict.update(response["id2ci"][i[0]])
            return_dict.update(response["id2ci"][i[1]])
            return_list.append(return_dict)
        
        return_json = {"result": return_list}
        with open('taizhang.txt', "w", encoding="utf-8") as f:
            json.dump(return_json, f, ensure_ascii=False)
            f.close()
        return Response("The information has been successfully updated locally.", mimetype='text/plain'), 200
    
    except Exception as e:
        logger.error(f"Error updating resource text: {str(e)}")
        return Response("The obtained information is abnormal.", mimetype='text/plain'), 500

# 固定接口，获取采集器版本号接口
@app.route('/version')
def version():
    return get_version()


if __name__ == '__main__':
    # 解密私钥，不用更改。
    private_key = '''**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''

    # 切换到采集器当前的目录
    if getattr(sys, 'frozen', False):
        os.chdir(os.path.dirname(sys.executable))
    else:
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 定义请求日志记录文件
    log_file = open('app.log', mode='a', encoding='utf-8')
    logging.basicConfig(
        stream=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    logger = logging.getLogger()

    # 程序启动函数
    app.run(host=args.host, port=args.port)
