package com.tongdao.datainterface.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;

/**
 * 资源台账同步请求DTO
 * 用于接收同步资源台账的请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResourceSyncRequestDto {
    
    /**
     * OMP系统域名
     */
    @JsonProperty("domain")
    @NotBlank(message = "域名不能为空")
    private String domain;
    
    /**
     * API路径
     */
    @JsonProperty("path")
    @NotBlank(message = "API路径不能为空")
    private String path;
    
    /**
     * API密钥
     */
    @JsonProperty("api_key")
    @NotBlank(message = "API密钥不能为空")
    private String apiKey;
    
    /**
     * API密钥（加密后的）
     */
    @JsonProperty("api_secret")
    @NotBlank(message = "API密钥不能为空")
    private String apiSecret;
    
    /**
     * 产品类型ID
     */
    @JsonProperty("product_type_id")
    private Integer productTypeId;
    
    /**
     * 页码
     */
    @JsonProperty("page")
    @Builder.Default
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @JsonProperty("page_size")
    @Builder.Default
    private Integer pageSize = 999999;
}
