package com.tongdao.datainterface.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 监控指标请求DTO
 * 用于接收获取监控指标的请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MetricsRequestDto {
    
    /**
     * 主机地址
     */
    @JsonProperty("host")
    @NotBlank(message = "主机地址不能为空")
    private String host;
    
    /**
     * 端口
     */
    @JsonProperty("port")
    @NotNull(message = "端口不能为空")
    @Min(value = 1, message = "端口必须大于0")
    @Max(value = 65535, message = "端口必须小于65536")
    private Integer port;
    
    /**
     * 用户名
     */
    @JsonProperty("username")
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    /**
     * 密码（加密后的）
     */
    @JsonProperty("password")
    @NotBlank(message = "密码不能为空")
    private String password;
    
    /**
     * 实例ID（可选）
     */
    @JsonProperty("instance_id")
    private String instanceId;
    
    /**
     * 是否返回Prometheus格式
     */
    @JsonProperty("prometheus_format")
    @Builder.Default
    private Boolean prometheusFormat = false;
}
