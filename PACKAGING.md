# JAR包打包策略说明

本项目支持多种打包策略，以适应不同的部署需求。

## 打包策略对比

| 策略 | 应用JAR大小 | 依赖大小 | 总大小 | 适用场景 |
|------|-------------|----------|--------|----------|
| 优化Fat JAR | ~35MB | 内嵌 | ~35MB | 单文件部署、容器化 |
| 分离式JAR | **0.13MB** | 35MB (外部) | ~35MB | **频繁更新、传统部署** |
| 超级瘦JAR | **0.13MB** | 分类存储 | ~35MB | 依赖管理、模块化部署 |
| 分层JAR | ~35MB | 内嵌分层 | ~35MB | Docker优化部署 |

### 🚀 分离式打包优势

**对于经常改动的项目，分离式打包是最佳选择：**

- **极小应用包**: 应用JAR仅130KB，更新传输极快
- **依赖复用**: 依赖包可在多个版本间共享
- **快速部署**: 只需更新130KB的应用JAR
- **版本管理**: 应用代码和依赖分离，便于版本控制

## 使用方法

### 1. 分离式JAR（默认，推荐）
```bash
mvn clean package -DskipTests
# 或者显式指定profile
mvn clean package -DskipTests -P thin-jar
```

**特点：**
- **应用JAR**: 仅130KB，包含业务代码
- **依赖分离**: 35MB依赖存放在 `target/lib/` 目录
- **启动方式**: `java -cp "app.jar;lib/*" MainClass`

**启动命令：**
```bash
# 直接启动
java -cp "target/dataInterface-0.0.1-SNAPSHOT.jar;target/lib/*" com.tongdao.datainterface.DataInterfaceApplication

# 使用启动脚本
scripts/start-thin.bat  # Windows
scripts/start-thin.sh   # Linux/Mac
```

**优势：**
- ⚡ **极速更新**: 只需传输130KB应用JAR
- 💾 **节省空间**: 依赖可在多版本间共享
- 🔄 **快速迭代**: 适合开发和频繁部署场景
- 📦 **依赖管理**: 依赖和应用代码完全分离

### 2. 优化Fat JAR
```bash
mvn clean package -DskipTests -P fat-jar
```

**特点：**
- 单个JAR文件包含所有依赖
- 启用分层支持，便于Docker缓存优化
- 可执行JAR，直接运行：`java -jar target/dataInterface-0.0.1-SNAPSHOT.jar`

### 3. 超级瘦JAR（依赖分类管理）
```bash
mvn clean package -DskipTests -P ultra-thin
```

**特点：**
- 应用JAR: 130KB
- Spring依赖: `target/lib/spring/` (11.58MB, 16个JAR)
- 第三方依赖: `target/lib/third-party/` (23.38MB, 35个JAR)
- 统一依赖: `target/lib/` (兼容性)

**优势：**
- 🏗️ **模块化管理**: 依赖按类型分类存储
- 🔧 **精细控制**: 可单独更新特定类型的依赖
- 📊 **清晰结构**: 便于依赖分析和管理

### 4. 分层JAR + Docker
```bash
# 构建分层JAR
mvn clean package -DskipTests -P optimized-jar

# 构建Docker镜像
docker build -f Dockerfile.layered -t datainterface:latest .
```

**特点：**
- 利用Docker分层缓存
- 依赖层变化少，应用层变化频繁
- 大幅提升Docker构建速度

## 分层信息查看

查看JAR的分层结构：
```bash
java -Djarmode=tools -jar target/dataInterface-0.0.1-SNAPSHOT.jar list-layers
```

输出结果：
- **dependencies**: 第三方依赖库 (~33MB, 45个JAR文件)
- **spring-boot-loader**: Spring Boot加载器
- **snapshot-dependencies**: 快照版本依赖
- **application**: 应用代码 (~0.13MB)

提取分层内容：
```bash
java -Djarmode=tools -jar target/dataInterface-0.0.1-SNAPSHOT.jar extract --layers --destination extracted
```

分层优势：
- **Docker缓存优化**: 依赖层变化少，应用层变化频繁，可以有效利用Docker分层缓存
- **增量更新**: 只需更新变化的分层，大幅减少传输和存储开销

## 性能对比

经过优化后的JAR包大小对比：
- **优化前**：37.6MB
- **优化后**：35.0MB
- **减少**：2.6MB (7%减少)

主要优化措施：
1. 移除未使用的 `simpleclient_httpserver` 依赖
2. 移除 `spring-boot-starter-actuator` 依赖
3. 启用分层JAR和压缩优化
4. 实现分离式打包，应用JAR从35MB减少到0.13MB

## 快速部署指南

### 分离式部署（默认模式）

1. **首次部署**：
```bash
# 构建分离式JAR（默认）
mvn clean package -DskipTests

# 部署到目标服务器
scripts/deploy-thin.bat  # 创建deploy目录并复制文件
```

2. **应用更新**（仅需传输130KB）：
```bash
# 重新构建应用（默认分离式）
mvn clean package -DskipTests

# 只复制应用JAR（130KB）
copy target/dataInterface-0.0.1-SNAPSHOT.jar deploy/

# 启动更新后的应用
scripts/start-deployed.bat
```

3. **启动时间对比**：
- Fat JAR启动: ~1.7秒
- 分离式JAR启动: ~1.7秒（性能相同）
- 传输时间: 35MB → 0.13MB（减少99.6%）

### 生产环境建议

- **开发/测试环境**: 使用分离式JAR（`thin-jar` profile）
- **生产环境**: 根据部署方式选择
  - 传统服务器: 分离式JAR
  - 容器化: 分层JAR + Docker
  - 单文件部署: 优化Fat JAR
