# Application Name
spring:
  application:
    name: dataInterface

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /

# Logging Configuration
logging:
  level:
    # 项目内部日志级别
    com:
      tongdao:
        datainterface: INFO
        # API日志相关Service和Interceptor的日志级别
        service:
          ApiLogService: INFO
        interceptor:
          ApiLoggingInterceptor: INFO
    # Spring Web 框架日志级别
    org:
      springframework:
        web: DEBUG
    # 自定义 API 日志标记的日志级别
    API_LOG: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n" # 控制台日志格式
  file:
    name: logs/api.log # API日志文件路径
  logback:
    rollingpolicy:
      max-file-size: 100MB # 每个日志文件最大大小
      max-history: 30 # 最多保留的日志文件数量

# Custom Application Configuration
app:
  version: 0.0.1
  name: dataInterface
  description: tongDao data backup app info