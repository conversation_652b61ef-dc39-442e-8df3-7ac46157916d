@echo off
REM 分离式部署脚本
REM 快速更新应用代码，无需重新下载依赖

set DEPLOY_DIR=deploy
set JAR_FILE=target\dataInterface-0.0.1-SNAPSHOT.jar
set LIB_DIR=target\lib

echo ========================================
echo   DataInterface 分离式部署工具
echo ========================================

REM 创建部署目录
if not exist "%DEPLOY_DIR%" mkdir "%DEPLOY_DIR%"
if not exist "%DEPLOY_DIR%\lib" mkdir "%DEPLOY_DIR%\lib"

REM 检查是否需要复制依赖
set COPY_DEPS=0
if not exist "%DEPLOY_DIR%\lib\*.jar" set COPY_DEPS=1

if %COPY_DEPS%==1 (
    echo 首次部署，复制所有依赖...
    copy "%LIB_DIR%\*.jar" "%DEPLOY_DIR%\lib\" >nul
    echo 依赖复制完成: %DEPLOY_DIR%\lib\
) else (
    echo 依赖已存在，跳过复制（节省时间）
)

REM 复制应用JAR
echo 更新应用代码...
copy "%JAR_FILE%" "%DEPLOY_DIR%\" >nul

REM 显示部署结果
echo ========================================
echo 部署完成！
echo ========================================
for %%F in ("%DEPLOY_DIR%\dataInterface-0.0.1-SNAPSHOT.jar") do set APP_SIZE=%%~zF
set /a APP_SIZE_KB=%APP_SIZE%/1024
echo 应用JAR: %APP_SIZE_KB% KB

echo.
echo 启动命令:
echo   cd %DEPLOY_DIR%
echo   java -cp "dataInterface-0.0.1-SNAPSHOT.jar;lib\*" com.tongdao.datainterface.DataInterfaceApplication
echo.
echo 或使用启动脚本:
echo   scripts\start-deployed.bat
