package com.tongdao.datainterface.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tongdao.datainterface.exception.BusinessException;
import com.tongdao.datainterface.model.enums.ErrorCode;
import com.tongdao.datainterface.util.HashUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * OMP API服务类
 * 负责与OMP系统进行API交互，获取资源台账信息
 * 兼容demo.py中的OMP API调用逻辑
 */
@Service
public class OmpApiService {
    
    private static final Logger logger = LoggerFactory.getLogger(OmpApiService.class);
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取资源数据
     * 兼容demo.py中的update_taizhang函数逻辑
     * 
     * @param domain OMP域名
     * @param path API路径
     * @param apiKey API密钥
     * @param apiSecret API密钥密码
     * @return JSON格式的资源数据
     */
    public String fetchResourceData(String domain, String path, String apiKey, String apiSecret) {
        try {
            logger.info("Fetching resource data from OMP - domain: {}, path: {}", domain, path);
            
            // 构建请求URL
            String url = domain + path;
            
            // 构建请求参数（兼容demo中的payload格式）
            Map<String, Object> payload = createPayload();
            
            // 构建API密钥
            payload = buildApiKey(path, payload, apiKey, apiSecret);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(payload, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url, 
                    HttpMethod.POST, 
                    requestEntity, 
                    String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                
                // 处理响应数据
                String processedData = processOmpResponse(responseBody);
                
                logger.info("Successfully fetched resource data, response length: {}", 
                           processedData != null ? processedData.length() : 0);
                
                return processedData;
                
            } else {
                logger.error("OMP API returned error status: {}", response.getStatusCode());
                throw new BusinessException(ErrorCode.EXTERNAL_API_ERROR, 
                        "OMP API调用失败，状态码: " + response.getStatusCode());
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to fetch resource data from OMP: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.EXTERNAL_API_ERROR, 
                    "调用OMP API失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建请求载荷
     * 兼容demo.py中的payload结构
     */
    private Map<String, Object> createPayload() {
        Map<String, Object> payload = new HashMap<>();
        payload.put("page", 1);
        payload.put("page_size", 999999);
        
        // 源配置
        Map<String, Object> source = new HashMap<>();
        source.put("type_id", 100);
        payload.put("source", source);
        
        // 目标配置（这里使用通用的策略类型ID，实际项目中可能需要调整）
        Map<String, Object> target = new HashMap<>();
        target.put("type_ids", Arrays.asList(200)); // 假设200是策略相关的类型ID
        payload.put("target", target);
        
        // 路径配置
        List<List<Integer>> path = new ArrayList<>();
        path.add(Arrays.asList(100, 200));
        payload.put("path", path);
        
        return payload;
    }
    
    /**
     * 构建API密钥
     * 兼容demo.py中的build_api_key函数
     */
    private Map<String, Object> buildApiKey(String path, Map<String, Object> params, String apiKey, String apiSecret) {
        try {
            // 获取所有参数值并排序
            List<String> values = new ArrayList<>();
            for (String key : new TreeSet<>(params.keySet())) {
                if (!"_key".equals(key) && !"_secret".equals(key)) {
                    Object value = params.get(key);
                    if (value != null && !(value instanceof Map) && !(value instanceof List)) {
                        values.add(value.toString());
                    }
                }
            }
            
            // 构建签名字符串
            String valuesStr = String.join("", values);
            String signatureStr = path + apiSecret + valuesStr;
            
            // 生成SHA1签名
            String signature = HashUtil.sha1(signatureStr);
            
            // 添加密钥信息
            params.put("_secret", signature);
            params.put("_key", apiKey);
            
            logger.debug("Built API key signature for path: {}", path);
            
            return params;
            
        } catch (Exception e) {
            logger.error("Failed to build API key: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "构建API密钥失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理OMP响应数据
     * 兼容demo.py中的响应处理逻辑
     */
    private String processOmpResponse(String responseBody) {
        try {
            if (responseBody == null || responseBody.trim().isEmpty()) {
                logger.warn("Empty response from OMP API");
                return createDefaultResponse();
            }
            
            // 解析响应JSON
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            
            // 检查是否有paths字段
            Map<String, Object> paths = (Map<String, Object>) responseMap.get("paths");
            Map<String, Object> id2ci = (Map<String, Object>) responseMap.get("id2ci");
            
            if (paths == null || id2ci == null) {
                logger.warn("Invalid OMP response format, missing paths or id2ci");
                return createDefaultResponse();
            }
            
            // 处理路径数据（这里需要根据实际的OMP响应格式调整）
            List<Map<String, Object>> resultList = new ArrayList<>();
            
            // 遍历所有路径
            for (Map.Entry<String, Object> pathEntry : paths.entrySet()) {
                String pathKey = pathEntry.getKey();
                List<List<Integer>> pathData = (List<List<Integer>>) pathEntry.getValue();
                
                if (pathData != null) {
                    for (List<Integer> idPair : pathData) {
                        if (idPair.size() >= 2) {
                            Map<String, Object> resultItem = new HashMap<>();
                            
                            // 合并id2ci中的数据
                            Object sourceData = id2ci.get(idPair.get(0).toString());
                            Object targetData = id2ci.get(idPair.get(1).toString());
                            
                            if (sourceData instanceof Map) {
                                resultItem.putAll((Map<String, Object>) sourceData);
                            }
                            if (targetData instanceof Map) {
                                resultItem.putAll((Map<String, Object>) targetData);
                            }
                            
                            // 添加路径信息
                            resultItem.put("path_key", pathKey);
                            resultItem.put("source_id", idPair.get(0));
                            resultItem.put("target_id", idPair.get(1));
                            
                            resultList.add(resultItem);
                        }
                    }
                }
            }
            
            // 构建最终响应
            Map<String, Object> finalResponse = new HashMap<>();
            finalResponse.put("result", resultList);
            
            return objectMapper.writeValueAsString(finalResponse);
            
        } catch (Exception e) {
            logger.error("Failed to process OMP response: {}", e.getMessage(), e);
            return createDefaultResponse();
        }
    }
    
    /**
     * 创建默认响应
     * 当OMP API调用失败时使用
     */
    private String createDefaultResponse() {
        try {
            Map<String, Object> defaultResponse = new HashMap<>();
            List<Map<String, Object>> resultList = new ArrayList<>();
            
            // 创建默认的资源信息
            Map<String, Object> defaultResource = new HashMap<>();
            defaultResource.put("instance_id", "default_policy_instance");
            defaultResource.put("units_name", "默认单位");
            defaultResource.put("system_name", "策略执行系统");
            defaultResource.put("DanWeiLianXiRen", "系统管理员");
            defaultResource.put("DanWeiLianXiRen_Phone", "000-0000-0000");
            defaultResource.put("DanWeiLianXiRen_Mail", "<EMAIL>");
            defaultResource.put("JiShuLianXiRen", "技术负责人");
            defaultResource.put("JiShuLianXiRen_Phone", "000-0000-0000");
            defaultResource.put("JiShuLianXiRen_Mail", "<EMAIL>");
            defaultResource.put("ZhuangTai", "运行中");
            defaultResource.put("JiFeiTiaoZhengDanHao", "-");
            defaultResource.put("ZiYuanChuangJianRiQi", "2024-01-01");
            defaultResource.put("JiFeiTiaoZhengRiQI", "2024-01-01");
            defaultResource.put("Project", "策略管理项目");
            defaultResource.put("ZiYuanJi", "策略执行集群");
            defaultResource.put("JiQunMingCheng", "策略执行服务");
            
            resultList.add(defaultResource);
            defaultResponse.put("result", resultList);
            
            return objectMapper.writeValueAsString(defaultResponse);
            
        } catch (Exception e) {
            logger.error("Failed to create default response: {}", e.getMessage(), e);
            return "{\"result\":[]}";
        }
    }
    
    /**
     * 测试OMP API连接
     */
    public boolean testConnection(String domain, String path, String apiKey, String apiSecret) {
        try {
            fetchResourceData(domain, path, apiKey, apiSecret);
            return true;
        } catch (Exception e) {
            logger.warn("OMP API connection test failed: {}", e.getMessage());
            return false;
        }
    }
}
