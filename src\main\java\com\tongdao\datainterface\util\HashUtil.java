package com.tongdao.datainterface.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 哈希工具类
 * 提供各种哈希算法的计算功能
 */
public class HashUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(HashUtil.class);
    
    /**
     * 计算字符串的SHA1哈希值
     * 
     * @param input 输入字符串
     * @return SHA1哈希值（十六进制字符串）
     */
    public static String sha1(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("SHA-1 algorithm not available", e);
            throw new RuntimeException("SHA-1算法不可用", e);
        }
    }
    
    /**
     * 计算字符串的SHA256哈希值
     * 
     * @param input 输入字符串
     * @return SHA256哈希值（十六进制字符串）
     */
    public static String sha256(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("SHA-256 algorithm not available", e);
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
    
    /**
     * 计算字符串的MD5哈希值
     * 
     * @param input 输入字符串
     * @return MD5哈希值（十六进制字符串）
     */
    public static String md5(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("MD5 algorithm not available", e);
            throw new RuntimeException("MD5算法不可用", e);
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
