package com.tongdao.datainterface.service;

import com.tongdao.datainterface.exception.BusinessException;
import com.tongdao.datainterface.model.enums.ErrorCode;
import com.tongdao.datainterface.util.CryptoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 加密解密服务类
 * 封装加密解密业务逻辑，提供统一的加密解密接口
 */
@Service
public class CryptoService {
    
    private static final Logger logger = LoggerFactory.getLogger(CryptoService.class);
    
    /**
     * 解密密码
     * 兼容Python demo中的解密逻辑
     * 
     * @param encryptedPassword 加密的密码（十六进制字符串）
     * @return 解密后的明文密码
     * @throws BusinessException 解密失败时抛出
     */
    public String decryptPassword(String encryptedPassword) {
        if (encryptedPassword == null || encryptedPassword.trim().isEmpty()) {
            throw BusinessException.badRequest("加密密码不能为空");
        }
        
        try {
            // 使用demo中的私钥进行解密
            String privateKey = CryptoUtil.getDemoPrivateKey();
            String decryptedPassword = CryptoUtil.decrypt(privateKey, encryptedPassword);
            
            logger.debug("Password decryption successful");
            return decryptedPassword;
            
        } catch (Exception e) {
            logger.error("Failed to decrypt password: {}", e.getMessage(), e);
            throw BusinessException.decryptFailed("密码解密失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证解密后的密码格式
     * 
     * @param password 解密后的密码
     * @return 是否为有效密码格式
     */
    public boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        
        // 基本密码格式验证
        String trimmedPassword = password.trim();
        return trimmedPassword.length() >= 6 && trimmedPassword.length() <= 50;
    }
    
    /**
     * 安全处理密码（用于日志记录等场景）
     * 
     * @param password 原始密码
     * @return 脱敏后的密码字符串
     */
    public String maskPassword(String password) {
        if (password == null || password.isEmpty()) {
            return "****";
        }
        
        if (password.length() <= 4) {
            return "****";
        }
        
        return password.substring(0, 2) + "****" + password.substring(password.length() - 2);
    }
    
    /**
     * 生成用于测试的模拟加密密码
     * 
     * @param plainPassword 明文密码
     * @return 模拟的加密密码（实际项目中应该使用真实的公钥加密）
     */
    public String generateMockEncryptedPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.trim().isEmpty()) {
            throw BusinessException.badRequest("明文密码不能为空");
        }
        
        try {
            // 为了测试方便，这里使用简单的十六进制编码模拟加密
            // 实际项目中应该使用真实的RSA公钥加密
            byte[] passwordBytes = plainPassword.getBytes("UTF-8");
            return CryptoUtil.byteArrayToHexString(passwordBytes);
            
        } catch (Exception e) {
            logger.error("Failed to generate mock encrypted password: {}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.ENCRYPT_FAILED, "生成模拟加密密码失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证加密密码格式
     * 
     * @param encryptedPassword 加密密码
     * @return 是否为有效的加密密码格式
     */
    public boolean isValidEncryptedPassword(String encryptedPassword) {
        if (encryptedPassword == null || encryptedPassword.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = encryptedPassword.trim();
        
        // 检查是否为有效的十六进制字符串
        if (trimmed.length() % 2 != 0) {
            return false;
        }
        
        // 检查是否只包含十六进制字符
        return trimmed.matches("^[0-9a-fA-F]+$");
    }
    
    /**
     * 批量解密密码
     * 
     * @param encryptedPasswords 加密密码列表
     * @return 解密后的密码列表
     */
    public java.util.List<String> decryptPasswords(java.util.List<String> encryptedPasswords) {
        if (encryptedPasswords == null || encryptedPasswords.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        java.util.List<String> decryptedPasswords = new java.util.ArrayList<>();
        
        for (String encryptedPassword : encryptedPasswords) {
            try {
                String decrypted = decryptPassword(encryptedPassword);
                decryptedPasswords.add(decrypted);
            } catch (BusinessException e) {
                logger.warn("Failed to decrypt password in batch: {}", e.getMessage());
                // 批量处理时，失败的密码用null占位
                decryptedPasswords.add(null);
            }
        }
        
        return decryptedPasswords;
    }
}
